<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RDR2 Bethnal Green Map - Exploration App</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts - IM Fell English with proper weights -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Ensure font loads before content -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=IM+Fell+English:ital,wght@0,400;1,400&display=swap');
    </style>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'rdr2': ['"IM Fell English"', 'Cinzel', 'serif'],
                    },
                    colors: {
                        'parchment': '#D8C7A0',
                        'parchment-dark': '#C4B490',
                        'leather': '#704214',
                        'ink': '#1A1A1A',
                        'road': '#3A2E24',
                        'water': '#A1A78B',
                        'water-dark': '#6C725F',
                        'icon': '#4E5A3E',
                        'building': '#C4B490'
                    }
                }
            }
        }
    </script>
</head>
<body class="font-rdr2 bg-parchment text-ink overflow-hidden">
    <!-- Parchment Overlay -->
    <div id="parchment-overlay" class="fixed inset-0 pointer-events-none z-[500] opacity-20"></div>
    
    <!-- Map Container -->
    <div id="map" class="w-full h-screen relative z-10"></div>
    
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 w-80 h-full bg-leather bg-opacity-90 text-parchment z-[600] p-6 overflow-y-auto border-r-2 border-road">
        <!-- Title -->
        <h1 class="text-2xl font-bold uppercase tracking-wider mb-6 text-center border-b-2 border-parchment pb-3">
            Bethnal Green Territory
        </h1>
        
        <!-- Search Section -->
        <div class="mb-6">
            <label for="search-input" class="block text-sm font-medium mb-2 uppercase tracking-wide">
                Search Bethnal Green
            </label>
            <div class="flex gap-2">
                <input
                    type="text"
                    id="search-input"
                    placeholder="Search pubs, shops, parks..."
                    class="flex-1 px-3 py-2 bg-parchment text-ink border-2 border-road rounded focus:outline-none focus:border-parchment-dark transition-colors duration-200"
                >
                <button 
                    id="search-btn"
                    class="px-4 py-2 bg-parchment text-ink border-2 border-road rounded hover:bg-parchment-dark transition-colors duration-200 font-medium uppercase tracking-wide"
                >
                    Search
                </button>
            </div>
        </div>
        
        <!-- Filters Section -->
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-3 uppercase tracking-wide border-b border-parchment pb-1">
                Show on Map
            </h3>
            <div class="space-y-2">
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="filter-saloons" checked class="w-4 h-4 text-parchment bg-leather border-parchment rounded focus:ring-parchment">
                    <span class="text-sm uppercase tracking-wide">Saloons (Restaurants)</span>
                </label>
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="filter-camps" checked class="w-4 h-4 text-parchment bg-leather border-parchment rounded focus:ring-parchment">
                    <span class="text-sm uppercase tracking-wide">Camps (Parks)</span>
                </label>
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="filter-stores" checked class="w-4 h-4 text-parchment bg-leather border-parchment rounded focus:ring-parchment">
                    <span class="text-sm uppercase tracking-wide">General Stores (Shops)</span>
                </label>
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="filter-doctors" checked class="w-4 h-4 text-parchment bg-leather border-parchment rounded focus:ring-parchment">
                    <span class="text-sm uppercase tracking-wide">Doctors (Pharmacies)</span>
                </label>
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="filter-trains" checked class="w-4 h-4 text-parchment bg-leather border-parchment rounded focus:ring-parchment">
                    <span class="text-sm uppercase tracking-wide">Trains (Stations)</span>
                </label>
            </div>
        </div>
        
        <!-- Exploration Progress -->
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-3 uppercase tracking-wide border-b border-parchment pb-1">
                Territory Survey
            </h3>
            <div id="exploration-progress" class="text-sm bg-parchment text-ink p-3 rounded border border-road">
                Territory Explored: 0.0%
            </div>
            <div class="mt-2 text-xs opacity-80">
                Move around to discover new areas
            </div>
        </div>

        <!-- Map Legend -->
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-3 uppercase tracking-wide border-b border-parchment pb-1">
                Map Legend
            </h3>
            <div class="space-y-2 text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-1 bg-road"></div>
                    <span>Main Trails</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 rounded-full" style="background-color: #8FBC8F;"></div>
                    <span>Green Spaces</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-1" style="background-color: #A1A78B;"></div>
                    <span>Waterways</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3" style="background-color: #4E5A3E;"></div>
                    <span>Landmarks</span>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div id="search-results" class="space-y-2">
            <h3 class="text-lg font-medium mb-3 uppercase tracking-wide border-b border-parchment pb-1">
                Local Establishments
            </h3>
            <div id="results-list" class="space-y-1">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6">
            <h3 class="text-lg font-medium mb-3 uppercase tracking-wide border-b border-parchment pb-1">
                Surveyor Tools
            </h3>
            <div class="space-y-2">
                <button id="center-map" class="w-full px-3 py-2 bg-parchment text-ink border-2 border-road rounded hover:bg-parchment-dark transition-colors duration-200 font-medium uppercase tracking-wide text-sm">
                    Return to Camp
                </button>
                <button id="toggle-labels" class="w-full px-3 py-2 bg-parchment text-ink border-2 border-road rounded hover:bg-parchment-dark transition-colors duration-200 font-medium uppercase tracking-wide text-sm">
                    Toggle All Labels
                </button>
            </div>
        </div>
    </div>
    
    <!-- Compass Rose -->
    <div id="compass-rose" class="fixed bottom-6 right-6 w-24 h-24 z-[600] pointer-events-none">
        <!-- Will be populated with SVG -->
    </div>
    
    <!-- Map Border -->
    <div id="map-border" class="fixed inset-0 pointer-events-none z-[550] border-4 border-road opacity-60"></div>
    
    <!-- Loading Indicator -->
    <div id="loading" class="fixed inset-0 bg-parchment bg-opacity-90 flex items-center justify-center z-[700]">
        <div class="text-center">
            <div class="text-2xl font-bold uppercase tracking-wider mb-2">Surveying Territory...</div>
            <div class="text-sm opacity-70 mb-4">Bethnal Green & Surrounds</div>
            <div class="w-16 h-16 border-4 border-leather border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
    </div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>
    
    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>
