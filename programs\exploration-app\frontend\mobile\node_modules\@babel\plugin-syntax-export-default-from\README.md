# @babel/plugin-syntax-export-default-from

> Allow parsing of export default from

See our website [@babel/plugin-syntax-export-default-from](https://babeljs.io/docs/babel-plugin-syntax-export-default-from) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-export-default-from
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-export-default-from --dev
```
