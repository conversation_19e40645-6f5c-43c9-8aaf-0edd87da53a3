{"bitwise": true, "camelcase": true, "curly": false, "eqeqeq": true, "es3": false, "forin": true, "immed": false, "indent": false, "latedef": "nofunc", "newcap": false, "noarg": true, "noempty": true, "nonew": false, "plusplus": false, "quotmark": true, "undef": true, "unused": "vars", "strict": false, "trailing": true, "maxparams": 5, "maxdepth": false, "maxstatements": false, "maxcomplexity": false, "maxlen": 100, "asi": true, "expr": true, "globalstrict": true, "smarttabs": true, "sub": true, "node": true, "globals": {"describe": false, "it": false, "before": false, "beforeEach": false, "after": false, "afterEach": false}}