# RDR2-Styled Bethnal Green Map Web App

A Red Dead Redemption 2 inspired map web application that transforms Bethnal Green, London into an authentic Wild West territory experience.

## Features

- 🗺️ **RDR2 Authentic Styling**: Parchment background, sepia tones, hand-drawn aesthetics
- 📍 **Bethnal Green Focus**: Centered on Bethnal Green, London with local area coverage
- 🔍 **Local Search**: Find pubs, shops, parks and services in the Bethnal Green area
- 🏪 **Themed Categories**: 
  - Saloons (Restaurants/Bars)
  - Camps (Parks/Recreation)
  - General Stores (Shops)
  - Doctors (Pharmacies/Medical)
  - Train Stations (Transportation)
- 🎨 **Custom Icons**: Hand-crafted SVG icons in RDR2 style
- 🧭 **Compass Rose**: Ornate 8-point compass for navigation
- 📱 **Responsive Design**: Works on desktop and mobile

## Color Palette

- **Parchment**: `#D8C7A0` (Background)
- **Parchment Dark**: `#C4B490` (Hover states)
- **Leather**: `#704214` (Sidebar background)
- **Ink**: `#1A1A1A` (Text)
- **Road**: `#3A2E24` (Borders, roads)
- **Water**: `#A1A78B` (Water features)
- **Icon**: `#4E5A3E` (Map icons)

## Typography

- **Primary Font**: IM Fell English (Google Fonts)
- **Fallback**: Cinzel
- **Minimum Size**: 16px for legibility
- **Style**: All-caps for major place names

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- Modern web browser with geolocation support

### Installation

1. Navigate to the web app directory:
```bash
cd frontend/web
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open your browser to `http://localhost:3000`

### Available Scripts

- `npm start` - Start live-server on port 3000
- `npm run dev` - Start with file watching
- `npm run build` - Prepare static files for deployment

## File Structure

```
frontend/web/
├── public/
│   ├── index.html          # Main HTML file
│   ├── styles.css          # RDR2 styling
│   ├── script.js           # Map logic and interactions
│   └── assets/
│       ├── marker-*.svg    # Custom RDR2-style icons
│       └── parchment.png   # Parchment texture (placeholder)
├── package.json
└── README.md
```

## Customization

### Adding New Location Types

1. Add mapping in `script.js`:
```javascript
const LOCATION_MAPPINGS = {
    your_type: { icon: 'your-icon', name: 'Your Display Name' }
};
```

2. Create corresponding SVG icon in `assets/marker-your-icon.svg`

3. Update filter categories in HTML and JavaScript

### Styling Modifications

All RDR2 styling is contained in `styles.css`. Key sections:
- `:root` variables for color palette
- `.leaflet-popup-*` for map popup styling
- `#sidebar` for interface styling
- Media queries for responsive design

## Browser Support

- Chrome/Edge 80+
- Firefox 75+
- Safari 13+
- Mobile browsers with geolocation support

## API Usage

- **OpenStreetMap**: Tile layer for map data
- **Nominatim**: Geocoding and place search
- **Leaflet.js**: Interactive map library

## Performance Notes

- Icons are optimized SVGs
- Map tiles load with fade-in animation
- Search results limited to 20 items
- Responsive design optimized for mobile

## Troubleshooting

### Geolocation Issues
- Ensure HTTPS for production deployment
- Check browser permissions for location access
- Fallback location: New York City

### Map Not Loading
- Check internet connection
- Verify Leaflet.js CDN availability
- Check browser console for errors

### Icons Not Displaying
- Verify SVG files exist in `assets/` directory
- Check file paths in `createCustomIcon()` function
- Ensure proper SVG syntax

## Future Enhancements

- [ ] Real parchment texture image
- [ ] Additional location categories
- [ ] Custom map overlays (buildings, roads)
- [ ] Offline map caching
- [ ] User location history
- [ ] Social sharing features

## License

Part of the Exploration App project - MIT License
