// RDR2 Local Map JavaScript

// Global variables
let map;
let userMarker;
let searchMarkers = [];
let currentResults = [];

// RDR2 Color Palette
const RDR2_COLORS = {
    parchment: '#D8C7A0',
    parchmentDark: '#C4B490',
    leather: '#704214',
    ink: '#1A1A1A',
    road: '#3A2E24',
    water: '#A1A78B',
    waterDark: '#6C725F',
    icon: '#4E5A3E',
    building: '#C4B490'
};

// Location type mappings for RDR2 theming (with British/London context)
const LOCATION_MAPPINGS = {
    restaurant: { icon: 'stew', name: 'Sal<PERSON>' },
    food: { icon: 'stew', name: 'Saloon' },
    bar: { icon: 'stew', name: '<PERSON>oon' },
    pub: { icon: 'stew', name: 'Saloon' },
    cafe: { icon: 'stew', name: 'Saloon' },
    fast_food: { icon: 'stew', name: 'Saloon' },
    takeaway: { icon: 'stew', name: 'Saloon' },
    park: { icon: 'tent', name: 'Camp' },
    recreation_ground: { icon: 'tent', name: 'Camp' },
    garden: { icon: 'tent', name: 'Camp' },
    common: { icon: 'tent', name: 'Camp' },
    green: { icon: 'tent', name: 'Camp' },
    shop: { icon: 'tshirt', name: 'General Store' },
    store: { icon: 'tshirt', name: 'General Store' },
    supermarket: { icon: 'tshirt', name: 'General Store' },
    convenience: { icon: 'tshirt', name: 'General Store' },
    grocery: { icon: 'tshirt', name: 'General Store' },
    butcher: { icon: 'tshirt', name: 'General Store' },
    bakery: { icon: 'tshirt', name: 'General Store' },
    pharmacy: { icon: 'cross', name: 'Doctor' },
    hospital: { icon: 'cross', name: 'Doctor' },
    clinic: { icon: 'cross', name: 'Doctor' },
    dentist: { icon: 'cross', name: 'Doctor' },
    doctors: { icon: 'cross', name: 'Doctor' },
    train_station: { icon: 'locomotive', name: 'Train Station' },
    railway_station: { icon: 'locomotive', name: 'Train Station' },
    subway_station: { icon: 'locomotive', name: 'Train Station' },
    underground: { icon: 'locomotive', name: 'Train Station' },
    tube: { icon: 'locomotive', name: 'Train Station' },
    overground: { icon: 'locomotive', name: 'Train Station' },
    hairdresser: { icon: 'scissors', name: 'Barber' },
    barber: { icon: 'scissors', name: 'Barber' },
    beauty: { icon: 'scissors', name: 'Barber' },
    hotel: { icon: 'bed', name: 'Hotel' },
    motel: { icon: 'bed', name: 'Hotel' },
    hostel: { icon: 'bed', name: 'Hotel' },
    guest_house: { icon: 'bed', name: 'Hotel' },
    theatre: { icon: 'masks', name: 'Theater' },
    cinema: { icon: 'masks', name: 'Theater' },
    arts_centre: { icon: 'masks', name: 'Theater' },
    post_office: { icon: 'envelope', name: 'Post Office' },
    museum: { icon: 'camera', name: 'Photo Studio' },
    gallery: { icon: 'camera', name: 'Photo Studio' },
    library: { icon: 'camera', name: 'Photo Studio' },
    church: { icon: 'pillars', name: 'Church' },
    chapel: { icon: 'pillars', name: 'Church' },
    cathedral: { icon: 'pillars', name: 'Church' },
    mosque: { icon: 'pillars', name: 'Church' },
    synagogue: { icon: 'pillars', name: 'Church' },
    temple: { icon: 'pillars', name: 'Church' }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    setupEventListeners();
    createCompassRose();
    setupParchmentOverlay();
});

// Initialize Leaflet map
function initializeMap() {
    // Create map with custom options
    map = L.map('map', {
        zoomControl: false,
        attributionControl: true,
        maxZoom: 19,
        minZoom: 10
    });

    // Add custom zoom control in bottom left
    L.control.zoom({
        position: 'bottomleft'
    }).addTo(map);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(map);

    // Get user location
    getUserLocation();
}

// Get user's current location or default to Bethnal Green
function getUserLocation() {
    // Bethnal Green coordinates (London, UK)
    const bethnalGreenLat = 51.5273;
    const bethnalGreenLng = -0.0556;

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                // Check if user is in London area (rough bounds)
                const isInLondon = (lat >= 51.3 && lat <= 51.7) && (lng >= -0.5 && lng <= 0.3);

                if (isInLondon) {
                    // Use user's actual location if in London
                    map.setView([lat, lng], 16);
                    addUserMarker(lat, lng);
                    searchNearbyPlaces(lat, lng);
                } else {
                    // Default to Bethnal Green if outside London
                    map.setView([bethnalGreenLat, bethnalGreenLng], 16);
                    addUserMarker(bethnalGreenLat, bethnalGreenLng);
                    searchNearbyPlaces(bethnalGreenLat, bethnalGreenLng);
                }

                hideLoading();
            },
            function(error) {
                console.warn('Geolocation error:', error);
                // Always fallback to Bethnal Green
                map.setView([bethnalGreenLat, bethnalGreenLng], 16);
                addUserMarker(bethnalGreenLat, bethnalGreenLng);
                searchNearbyPlaces(bethnalGreenLat, bethnalGreenLng);
                hideLoading();
            }
        );
    } else {
        console.warn('Geolocation not supported');
        // Always fallback to Bethnal Green
        map.setView([bethnalGreenLat, bethnalGreenLng], 16);
        addUserMarker(bethnalGreenLat, bethnalGreenLng);
        searchNearbyPlaces(bethnalGreenLat, bethnalGreenLng);
        hideLoading();
    }
}

// Add user marker (camp icon)
function addUserMarker(lat, lng) {
    const campIcon = createCustomIcon('star', 32);
    
    userMarker = L.marker([lat, lng], { 
        icon: campIcon,
        zIndexOffset: 1000
    }).addTo(map);
    
    userMarker.bindPopup(`
        <div class="text-center">
            <h3 class="font-bold uppercase tracking-wide text-lg mb-2">YOUR CAMP</h3>
            <p class="text-sm">Bethnal Green Territory</p>
            <p class="text-xs opacity-70 mt-1">East London Frontier</p>
        </div>
    `);
}

// Create custom RDR2-style icon
function createCustomIcon(iconType, size = 24) {
    const iconUrl = `assets/marker-${iconType}.svg`;
    
    return L.icon({
        iconUrl: iconUrl,
        iconSize: [size, size],
        iconAnchor: [size/2, size],
        popupAnchor: [0, -size],
        className: 'rdr2-marker'
    });
}

// Search nearby places using Nominatim API (focused on Bethnal Green area)
async function searchNearbyPlaces(lat, lng, query = '') {
    try {
        const radius = 0.015; // Slightly larger radius for better coverage
        const bbox = `${lng - radius},${lat - radius},${lng + radius},${lat + radius}`;

        let searchQuery = query || 'restaurant,shop,pharmacy,park,train_station,pub,cafe,bar';

        // Add Bethnal Green context to search for better local results
        const locationContext = query ? `${query} Bethnal Green London` : `${searchQuery} Bethnal Green London`;

        const url = `https://nominatim.openstreetmap.org/search?format=json&limit=100&bounded=1&viewbox=${bbox}&q=${locationContext}&addressdetails=1&extratags=1`;

        const response = await fetch(url);
        const data = await response.json();

        // Filter results to prioritize Bethnal Green and nearby areas
        currentResults = data.filter(place => {
            if (!place.lat || !place.lon) return false;

            // Check if place is in Bethnal Green or nearby areas
            const address = place.display_name.toLowerCase();
            const isInArea = address.includes('bethnal green') ||
                           address.includes('tower hamlets') ||
                           address.includes('shoreditch') ||
                           address.includes('whitechapel') ||
                           address.includes('cambridge heath') ||
                           address.includes('london borough');

            return isInArea;
        });

        // If no local results, fall back to coordinate-based search
        if (currentResults.length === 0) {
            const fallbackUrl = `https://nominatim.openstreetmap.org/search?format=json&limit=50&bounded=1&viewbox=${bbox}&q=${searchQuery}`;
            const fallbackResponse = await fetch(fallbackUrl);
            const fallbackData = await fallbackResponse.json();
            currentResults = fallbackData.filter(place => place.lat && place.lon);
        }

        updateSearchResults();
        updateMapMarkers();

    } catch (error) {
        console.error('Search error:', error);
        // Show user-friendly error message
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = '<div class="result-item">Unable to load nearby places. Please check your connection.</div>';
    }
}

// Update search results in sidebar
function updateSearchResults() {
    const resultsList = document.getElementById('results-list');
    resultsList.innerHTML = '';
    
    const filteredResults = filterResultsByCategory();
    
    filteredResults.slice(0, 20).forEach((place, index) => {
        const mapping = getLocationMapping(place);
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        resultItem.innerHTML = `
            <div class="font-medium uppercase tracking-wide text-sm">${mapping.name}</div>
            <div class="text-xs opacity-80">${place.display_name.split(',')[0]}</div>
        `;
        
        resultItem.addEventListener('click', () => {
            map.setView([parseFloat(place.lat), parseFloat(place.lon)], 17);
            // Find and open the corresponding marker popup
            const marker = searchMarkers.find(m => 
                m.getLatLng().lat === parseFloat(place.lat) && 
                m.getLatLng().lng === parseFloat(place.lon)
            );
            if (marker) {
                marker.openPopup();
            }
        });
        
        resultsList.appendChild(resultItem);
    });
}

// Filter results by selected categories
function filterResultsByCategory() {
    const filters = {
        saloons: document.getElementById('filter-saloons').checked,
        camps: document.getElementById('filter-camps').checked,
        stores: document.getElementById('filter-stores').checked,
        doctors: document.getElementById('filter-doctors').checked,
        trains: document.getElementById('filter-trains').checked
    };
    
    return currentResults.filter(place => {
        const mapping = getLocationMapping(place);
        const category = getCategoryFromMapping(mapping);
        return filters[category];
    });
}

// Get location mapping for RDR2 theming
function getLocationMapping(place) {
    const type = place.type || '';
    const category = place.category || '';
    const className = place.class || '';
    
    // Check various fields for location type
    const searchFields = [type, category, className].join(' ').toLowerCase();
    
    for (const [key, mapping] of Object.entries(LOCATION_MAPPINGS)) {
        if (searchFields.includes(key)) {
            return mapping;
        }
    }
    
    // Default mapping
    return { icon: 'x', name: 'Unknown Location' };
}

// Get category from mapping for filtering
function getCategoryFromMapping(mapping) {
    if (mapping.name.includes('Saloon')) return 'saloons';
    if (mapping.name.includes('Camp')) return 'camps';
    if (mapping.name.includes('Store')) return 'stores';
    if (mapping.name.includes('Doctor')) return 'doctors';
    if (mapping.name.includes('Train')) return 'trains';
    return 'stores'; // Default
}

// Update map markers
function updateMapMarkers() {
    // Clear existing markers
    searchMarkers.forEach(marker => map.removeLayer(marker));
    searchMarkers = [];
    
    const filteredResults = filterResultsByCategory();
    
    filteredResults.forEach(place => {
        const mapping = getLocationMapping(place);
        const icon = createCustomIcon(mapping.icon, 24);
        
        const marker = L.marker([parseFloat(place.lat), parseFloat(place.lon)], { 
            icon: icon 
        }).addTo(map);
        
        marker.bindPopup(`
            <div class="text-center">
                <h3 class="font-bold uppercase tracking-wide text-base mb-2">${mapping.name}</h3>
                <p class="text-sm font-medium">${place.display_name.split(',')[0]}</p>
                <p class="text-xs opacity-70 mt-1">${place.display_name.split(',').slice(1, 3).join(',')}</p>
            </div>
        `);
        
        searchMarkers.push(marker);
    });
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // Filter checkboxes
    const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSearchResults();
            updateMapMarkers();
        });
    });
}

// Perform search
async function performSearch() {
    const query = document.getElementById('search-input').value.trim();
    if (!query) return;
    
    const center = map.getCenter();
    await searchNearbyPlaces(center.lat, center.lng, query);
}

// Create compass rose SVG
function createCompassRose() {
    const compassContainer = document.getElementById('compass-rose');
    compassContainer.innerHTML = `
        <svg viewBox="0 0 100 100" class="w-full h-full">
            <defs>
                <style>
                    .compass-text { font-family: 'IM Fell English', serif; font-size: 8px; font-weight: bold; fill: ${RDR2_COLORS.icon}; }
                    .compass-line { stroke: ${RDR2_COLORS.icon}; stroke-width: 1.5; }
                    .compass-point { fill: ${RDR2_COLORS.icon}; }
                </style>
            </defs>
            
            <!-- Outer circle -->
            <circle cx="50" cy="50" r="45" fill="none" class="compass-line"/>
            
            <!-- Main compass points -->
            <line x1="50" y1="10" x2="50" y2="20" class="compass-line"/>
            <line x1="50" y1="80" x2="50" y2="90" class="compass-line"/>
            <line x1="10" y1="50" x2="20" y2="50" class="compass-line"/>
            <line x1="80" y1="50" x2="90" y2="50" class="compass-line"/>
            
            <!-- Diagonal points -->
            <line x1="21.7" y1="21.7" x2="28.3" y2="28.3" class="compass-line"/>
            <line x1="78.3" y1="21.7" x2="71.7" y2="28.3" class="compass-line"/>
            <line x1="21.7" y1="78.3" x2="28.3" y2="71.7" class="compass-line"/>
            <line x1="78.3" y1="78.3" x2="71.7" y2="71.7" class="compass-line"/>
            
            <!-- Direction labels -->
            <text x="50" y="8" text-anchor="middle" class="compass-text">N</text>
            <text x="50" y="96" text-anchor="middle" class="compass-text">S</text>
            <text x="8" y="54" text-anchor="middle" class="compass-text">W</text>
            <text x="92" y="54" text-anchor="middle" class="compass-text">E</text>
            
            <!-- Center ornament -->
            <circle cx="50" cy="50" r="3" class="compass-point"/>
        </svg>
    `;
}

// Setup parchment overlay
function setupParchmentOverlay() {
    // Create a simple parchment pattern using CSS if image is not available
    const overlay = document.getElementById('parchment-overlay');
    overlay.style.backgroundImage = `
        radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, rgba(210, 180, 140, 0.05) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(210, 180, 140, 0.05) 25%, transparent 25%)
    `;
}

// Hide loading screen
function hideLoading() {
    const loading = document.getElementById('loading');
    loading.style.opacity = '0';
    setTimeout(() => {
        loading.style.display = 'none';
    }, 500);
}
