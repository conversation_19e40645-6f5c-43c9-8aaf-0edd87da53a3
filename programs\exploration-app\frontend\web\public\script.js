// RDR2 Local Map JavaScript

// Global variables
let map;
let userMarker;
let searchMarkers = [];
let currentResults = [];

// RDR2 Color Palette
const RDR2_COLORS = {
    parchment: '#D8C7A0',
    parchmentDark: '#C4B490',
    leather: '#704214',
    ink: '#1A1A1A',
    road: '#3A2E24',
    water: '#A1A78B',
    waterDark: '#6C725F',
    icon: '#4E5A3E',
    building: '#C4B490'
};

// Location type mappings for RDR2 theming (with British/London context)
const LOCATION_MAPPINGS = {
    restaurant: { icon: 'stew', name: 'Sal<PERSON>' },
    food: { icon: 'stew', name: 'Saloon' },
    bar: { icon: 'stew', name: '<PERSON>oon' },
    pub: { icon: 'stew', name: 'Saloon' },
    cafe: { icon: 'stew', name: 'Saloon' },
    fast_food: { icon: 'stew', name: 'Saloon' },
    takeaway: { icon: 'stew', name: 'Saloon' },
    park: { icon: 'tent', name: 'Camp' },
    recreation_ground: { icon: 'tent', name: 'Camp' },
    garden: { icon: 'tent', name: 'Camp' },
    common: { icon: 'tent', name: 'Camp' },
    green: { icon: 'tent', name: 'Camp' },
    shop: { icon: 'tshirt', name: 'General Store' },
    store: { icon: 'tshirt', name: 'General Store' },
    supermarket: { icon: 'tshirt', name: 'General Store' },
    convenience: { icon: 'tshirt', name: 'General Store' },
    grocery: { icon: 'tshirt', name: 'General Store' },
    butcher: { icon: 'tshirt', name: 'General Store' },
    bakery: { icon: 'tshirt', name: 'General Store' },
    pharmacy: { icon: 'cross', name: 'Doctor' },
    hospital: { icon: 'cross', name: 'Doctor' },
    clinic: { icon: 'cross', name: 'Doctor' },
    dentist: { icon: 'cross', name: 'Doctor' },
    doctors: { icon: 'cross', name: 'Doctor' },
    train_station: { icon: 'locomotive', name: 'Train Station' },
    railway_station: { icon: 'locomotive', name: 'Train Station' },
    subway_station: { icon: 'locomotive', name: 'Train Station' },
    underground: { icon: 'locomotive', name: 'Train Station' },
    tube: { icon: 'locomotive', name: 'Train Station' },
    overground: { icon: 'locomotive', name: 'Train Station' },
    hairdresser: { icon: 'scissors', name: 'Barber' },
    barber: { icon: 'scissors', name: 'Barber' },
    beauty: { icon: 'scissors', name: 'Barber' },
    hotel: { icon: 'bed', name: 'Hotel' },
    motel: { icon: 'bed', name: 'Hotel' },
    hostel: { icon: 'bed', name: 'Hotel' },
    guest_house: { icon: 'bed', name: 'Hotel' },
    theatre: { icon: 'masks', name: 'Theater' },
    cinema: { icon: 'masks', name: 'Theater' },
    arts_centre: { icon: 'masks', name: 'Theater' },
    post_office: { icon: 'envelope', name: 'Post Office' },
    museum: { icon: 'camera', name: 'Photo Studio' },
    gallery: { icon: 'camera', name: 'Photo Studio' },
    library: { icon: 'camera', name: 'Photo Studio' },
    church: { icon: 'pillars', name: 'Church' },
    chapel: { icon: 'pillars', name: 'Church' },
    cathedral: { icon: 'pillars', name: 'Church' },
    mosque: { icon: 'pillars', name: 'Church' },
    synagogue: { icon: 'pillars', name: 'Church' },
    temple: { icon: 'pillars', name: 'Church' }
};

// Minimal application initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, creating minimal map...');

    // Hide loading screen
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = 'none';
    }

    // Create minimal working map
    try {
        console.log('Creating map...');
        const map = L.map('map').setView([51.5273, -0.0556], 16);

        console.log('Adding tiles...');
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        console.log('Adding custom Bethnal Green features...');

        // Add major roads
        const roads = [
            { name: "Roman Road", path: [[51.5250, -0.0680], [51.5280, -0.0400]], type: "major" },
            { name: "Bethnal Green Road", path: [[51.5190, -0.0700], [51.5220, -0.0400]], type: "major" },
            { name: "Cambridge Heath Road", path: [[51.5160, -0.0550], [51.5380, -0.0520]], type: "major" }
        ];

        roads.forEach(road => {
            const roadLine = L.polyline(road.path, {
                color: '#3A2E24',
                weight: 8,
                opacity: 0.8,
                dashArray: '3, 2',
                lineCap: 'round'
            }).addTo(map);

            roadLine.bindPopup(`<b>${road.name}</b><br>Main Trail`);
        });

        // Add parks
        const parks = [
            { name: "Victoria Park", center: [51.5340, -0.0430], radius: 0.004 },
            { name: "Bethnal Green Gardens", center: [51.5270, -0.0560], radius: 0.0015 }
        ];

        parks.forEach(park => {
            L.circle(park.center, {
                radius: park.radius * 111000,
                color: '#4E5A3E',
                weight: 3,
                opacity: 0.8,
                fillColor: '#8FBC8F',
                fillOpacity: 0.5,
                dashArray: '4, 2'
            }).addTo(map).bindPopup(`<b>${park.name}</b><br>Green Space`);
        });

        // Add territory labels
        const territoryLabels = [
            { text: "BETHNAL GREEN TERRITORY", pos: [51.5350, -0.0550], size: "20px" },
            { text: "EAST LONDON FRONTIER", pos: [51.5160, -0.0550], size: "12px" },
            { text: "NORTH", pos: [51.5390, -0.0550], size: "14px" }
        ];

        territoryLabels.forEach(label => {
            const textIcon = L.divIcon({
                className: 'territory-label',
                html: `<span style="
                    font-family: 'IM Fell English', serif;
                    font-size: ${label.size};
                    font-weight: bold;
                    color: #3A2E24;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.7), 1px 1px 2px rgba(255,255,255,0.3);
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    background: rgba(216, 199, 160, 0.8);
                    padding: 4px 8px;
                    border-radius: 4px;
                    border: 2px solid rgba(58, 46, 36, 0.4);
                ">${label.text}</span>`,
                iconSize: [label.text.length * 8, 30],
                iconAnchor: [label.text.length * 4, 15]
            });

            L.marker(label.pos, { icon: textIcon }).addTo(map);
        });

        // Add main camp marker
        L.marker([51.5273, -0.0556])
            .addTo(map)
            .bindPopup('<b>Bethnal Green Territory</b><br>Your Camp')
            .openPopup();

        console.log('Map created successfully!');

    } catch (error) {
        console.error('Error creating map:', error);
        document.getElementById('map').innerHTML = '<div style="padding: 20px; text-align: center; font-size: 18px;">Error loading map: ' + error.message + '</div>';
    }
});

// Add subtle sound effects (optional)
function addSoundEffects() {
    // Create audio context for subtle UI sounds
    let audioContext;

    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
        console.log('Audio not supported');
        return;
    }

    // Function to create simple beep sound
    function playSound(frequency = 440, duration = 100, volume = 0.1) {
        if (!audioContext) return;

        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 1000);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);
    }

    // Add sound to marker clicks
    map.on('popupopen', () => playSound(660, 150, 0.05));

    // Add sound to zoom changes
    map.on('zoomend', () => playSound(440, 100, 0.03));
}

// Add keyboard shortcuts
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + M: Toggle measurement mode
        if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
            e.preventDefault();
            const measureBtn = document.getElementById('measure-btn');
            if (measureBtn) measureBtn.click();
        }

        // Ctrl/Cmd + H: Return to center
        if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
            e.preventDefault();
            const centerBtn = document.getElementById('center-map');
            if (centerBtn) centerBtn.click();
        }

        // Ctrl/Cmd + L: Toggle labels
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            const toggleBtn = document.getElementById('toggle-labels');
            if (toggleBtn) toggleBtn.click();
        }

        // Arrow keys for map navigation
        if (e.key.startsWith('Arrow')) {
            e.preventDefault();
            const center = map.getCenter();
            const offset = 0.001;

            switch (e.key) {
                case 'ArrowUp':
                    map.setView([center.lat + offset, center.lng]);
                    break;
                case 'ArrowDown':
                    map.setView([center.lat - offset, center.lng]);
                    break;
                case 'ArrowLeft':
                    map.setView([center.lat, center.lng - offset]);
                    break;
                case 'ArrowRight':
                    map.setView([center.lat, center.lng + offset]);
                    break;
            }
        }
    });
}

// Add performance optimizations
function addPerformanceOptimizations() {
    // Debounce zoom events
    let zoomTimeout;
    map.on('zoom', function() {
        clearTimeout(zoomTimeout);
        zoomTimeout = setTimeout(updateLabelVisibility, 100);
    });

    // Lazy load markers based on viewport
    map.on('moveend', function() {
        const bounds = map.getBounds();

        map.eachLayer(layer => {
            if (layer.getLatLng && layer.options.icon) {
                const markerBounds = bounds.contains(layer.getLatLng());
                const element = layer.getElement();

                if (element) {
                    element.style.display = markerBounds ? 'block' : 'none';
                }
            }
        });
    });

    // Add loading states for better UX
    map.on('zoomstart movestart', function() {
        document.body.style.cursor = 'wait';
    });

    map.on('zoomend moveend', function() {
        document.body.style.cursor = 'default';
    });
}

// Initialize simple working map
function initializeSimpleMap() {
    console.log('Creating simple map...');

    // Create basic map
    map = L.map('map').setView([51.5273, -0.0556], 16);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(map);

    // Add a simple marker
    L.marker([51.5273, -0.0556])
        .addTo(map)
        .bindPopup('<b>Bethnal Green</b><br>Map is working!')
        .openPopup();

    console.log('Simple map created successfully');
}

// Initialize Leaflet map with RDR2 styling and full Bethnal Green coverage
function initializeMap() {
    // Define expanded Bethnal Green boundaries for complete coverage
    const bethnalGreenBounds = [
        [51.5140, -0.0720], // Southwest corner (expanded)
        [51.5420, -0.0380]  // Northeast corner (expanded)
    ];

    // Create map with custom options for detailed view
    map = L.map('map', {
        zoomControl: false,
        attributionControl: true,
        maxZoom: 19, // Higher max zoom for detailed labels
        minZoom: 14,
        scrollWheelZoom: true,
        doubleClickZoom: true, // Enable for detailed exploration
        boxZoom: false,
        keyboard: true, // Enable keyboard navigation
        // Restrict map to expanded Bethnal Green area
        maxBounds: bethnalGreenBounds,
        maxBoundsViscosity: 0.8 // Slightly more flexible bounds
    });

    // Add custom RDR2-styled zoom control
    const customZoomControl = L.control.zoom({
        position: 'bottomleft',
        zoomInTitle: 'Survey Closer',
        zoomOutTitle: 'Survey Wider'
    });
    customZoomControl.addTo(map);

    // Style the zoom control buttons
    setTimeout(() => {
        const zoomButtons = document.querySelectorAll('.leaflet-control-zoom a');
        zoomButtons.forEach(button => {
            button.style.backgroundColor = 'var(--leather)';
            button.style.color = 'var(--parchment)';
            button.style.border = '2px solid var(--road)';
            button.style.borderRadius = '0';
            button.style.fontFamily = '"IM Fell English", serif';
            button.style.fontWeight = 'bold';
            button.style.textShadow = '1px 1px 2px rgba(0,0,0,0.5)';
        });
    }, 100);

    // Add OpenStreetMap tiles as base layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© Bethnal Green Territory Survey',
        maxZoom: 19,
        className: 'rdr2-tiles'
    }).addTo(map);

    // Create custom Bethnal Green overlays
    try {
        createCustomBethnalGreenMap();
        console.log('Custom map overlays created successfully');
    } catch (error) {
        console.error('Error creating custom map overlays:', error);
        // Continue with base map
    }

    // Remove blur effects on zoom for better clarity
    map.on('zoomstart', function() {
        // Removed blur effect for better readability
    });

    map.on('zoomend', function() {
        // Removed blur effect for better readability
    });

    // Add a test marker to ensure map is working
    const testMarker = L.marker([51.5273, -0.0556]).addTo(map);
    testMarker.bindPopup('<b>Bethnal Green Center</b><br>Map is working!');

    // Center map on Bethnal Green
    map.setView([51.5273, -0.0556], 16);

    // Get user location
    getUserLocation();
}

// Get user's current location or default to Bethnal Green (restricted to area)
function getUserLocation() {
    // Bethnal Green center coordinates
    const bethnalGreenLat = 51.5273;
    const bethnalGreenLng = -0.0556;

    // Define expanded Bethnal Green area bounds
    const bethnalGreenBounds = {
        north: 51.5420,
        south: 51.5140,
        east: -0.0380,
        west: -0.0720
    };

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                // Check if user is specifically in Bethnal Green area
                const isInBethnalGreen = (
                    lat >= bethnalGreenBounds.south &&
                    lat <= bethnalGreenBounds.north &&
                    lng >= bethnalGreenBounds.west &&
                    lng <= bethnalGreenBounds.east
                );

                if (isInBethnalGreen) {
                    // Use user's actual location if in Bethnal Green
                    map.setView([lat, lng], 16);
                    addUserMarker(lat, lng);
                    // Skip search for now to avoid hanging
                    // searchNearbyPlaces(lat, lng);
                } else {
                    // Always center on Bethnal Green if outside the area
                    map.setView([bethnalGreenLat, bethnalGreenLng], 16);
                    addUserMarker(bethnalGreenLat, bethnalGreenLng);
                    // Skip search for now to avoid hanging
                    // searchNearbyPlaces(bethnalGreenLat, bethnalGreenLng);
                }

                hideLoading();
            },
            function(error) {
                console.warn('Geolocation error:', error);
                // Always center on Bethnal Green
                map.setView([bethnalGreenLat, bethnalGreenLng], 16);
                addUserMarker(bethnalGreenLat, bethnalGreenLng);
                hideLoading();
            }
        );
    } else {
        console.warn('Geolocation not supported');
        // Always center on Bethnal Green
        map.setView([bethnalGreenLat, bethnalGreenLng], 16);
        addUserMarker(bethnalGreenLat, bethnalGreenLng);
        hideLoading();
    }
}

// Add user marker (camp icon)
function addUserMarker(lat, lng) {
    const campIcon = createCustomIcon('star', 32);
    
    userMarker = L.marker([lat, lng], { 
        icon: campIcon,
        zIndexOffset: 1000
    }).addTo(map);
    
    userMarker.bindPopup(`
        <div class="text-center">
            <h3 class="font-bold uppercase tracking-wide text-lg mb-2">YOUR CAMP</h3>
            <p class="text-sm">Bethnal Green Territory</p>
            <p class="text-xs opacity-70 mt-1">East London Frontier</p>
        </div>
    `);
}

// Create custom RDR2-style icon
function createCustomIcon(iconType, size = 24) {
    const iconUrl = `assets/marker-${iconType}.svg`;
    
    return L.icon({
        iconUrl: iconUrl,
        iconSize: [size, size],
        iconAnchor: [size/2, size],
        popupAnchor: [0, -size],
        className: 'rdr2-marker'
    });
}

// Search places specifically within Bethnal Green boundaries
async function searchNearbyPlaces(lat, lng, query = '') {
    try {
        // Strict Bethnal Green boundaries
        const bethnalGreenBounds = {
            west: -0.0700,
            south: 51.5150,
            east: -0.0400,
            north: 51.5400
        };

        const bbox = `${bethnalGreenBounds.west},${bethnalGreenBounds.south},${bethnalGreenBounds.east},${bethnalGreenBounds.north}`;

        let searchQuery = query || 'restaurant,shop,pharmacy,park,train_station,pub,cafe,bar';

        // Search specifically within Bethnal Green
        const locationContext = `${searchQuery} Bethnal Green London E2`;

        const url = `https://nominatim.openstreetmap.org/search?format=json&limit=100&bounded=1&viewbox=${bbox}&q=${locationContext}&addressdetails=1&extratags=1`;

        const response = await fetch(url);
        const data = await response.json();

        // Strict filtering to only include places within Bethnal Green bounds
        currentResults = data.filter(place => {
            if (!place.lat || !place.lon) return false;

            const placeLat = parseFloat(place.lat);
            const placeLng = parseFloat(place.lon);

            // Check if coordinates are within strict Bethnal Green bounds
            const isWithinBounds = (
                placeLat >= bethnalGreenBounds.south &&
                placeLat <= bethnalGreenBounds.north &&
                placeLng >= bethnalGreenBounds.west &&
                placeLng <= bethnalGreenBounds.east
            );

            // Also check address for Bethnal Green references
            const address = place.display_name.toLowerCase();
            const isInBethnalGreen = address.includes('bethnal green') ||
                                   address.includes('e2') ||
                                   (address.includes('tower hamlets') && isWithinBounds);

            return isWithinBounds && isInBethnalGreen;
        });

        // If no specific results, do a coordinate-only search within bounds
        if (currentResults.length === 0) {
            const fallbackUrl = `https://nominatim.openstreetmap.org/search?format=json&limit=80&bounded=1&viewbox=${bbox}&q=${searchQuery}`;
            const fallbackResponse = await fetch(fallbackUrl);
            const fallbackData = await fallbackResponse.json();

            currentResults = fallbackData.filter(place => {
                if (!place.lat || !place.lon) return false;
                const placeLat = parseFloat(place.lat);
                const placeLng = parseFloat(place.lon);

                return (
                    placeLat >= bethnalGreenBounds.south &&
                    placeLat <= bethnalGreenBounds.north &&
                    placeLng >= bethnalGreenBounds.west &&
                    placeLng <= bethnalGreenBounds.east
                );
            });
        }

        updateSearchResults();
        updateMapMarkers();

    } catch (error) {
        console.error('Search error:', error);
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = '<div class="result-item">Unable to load Bethnal Green establishments. Please check your connection.</div>';
    }
}

// Update search results in sidebar
function updateSearchResults() {
    const resultsList = document.getElementById('results-list');
    resultsList.innerHTML = '';
    
    const filteredResults = filterResultsByCategory();
    
    filteredResults.slice(0, 20).forEach((place, index) => {
        const mapping = getLocationMapping(place);
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        resultItem.innerHTML = `
            <div class="font-medium uppercase tracking-wide text-sm">${mapping.name}</div>
            <div class="text-xs opacity-80">${place.display_name.split(',')[0]}</div>
        `;
        
        resultItem.addEventListener('click', () => {
            map.setView([parseFloat(place.lat), parseFloat(place.lon)], 17);
            // Find and open the corresponding marker popup
            const marker = searchMarkers.find(m => 
                m.getLatLng().lat === parseFloat(place.lat) && 
                m.getLatLng().lng === parseFloat(place.lon)
            );
            if (marker) {
                marker.openPopup();
            }
        });
        
        resultsList.appendChild(resultItem);
    });
}

// Filter results by selected categories
function filterResultsByCategory() {
    const filters = {
        saloons: document.getElementById('filter-saloons').checked,
        camps: document.getElementById('filter-camps').checked,
        stores: document.getElementById('filter-stores').checked,
        doctors: document.getElementById('filter-doctors').checked,
        trains: document.getElementById('filter-trains').checked
    };
    
    return currentResults.filter(place => {
        const mapping = getLocationMapping(place);
        const category = getCategoryFromMapping(mapping);
        return filters[category];
    });
}

// Get location mapping for RDR2 theming
function getLocationMapping(place) {
    const type = place.type || '';
    const category = place.category || '';
    const className = place.class || '';
    
    // Check various fields for location type
    const searchFields = [type, category, className].join(' ').toLowerCase();
    
    for (const [key, mapping] of Object.entries(LOCATION_MAPPINGS)) {
        if (searchFields.includes(key)) {
            return mapping;
        }
    }
    
    // Default mapping
    return { icon: 'x', name: 'Unknown Location' };
}

// Get category from mapping for filtering
function getCategoryFromMapping(mapping) {
    if (mapping.name.includes('Saloon')) return 'saloons';
    if (mapping.name.includes('Camp')) return 'camps';
    if (mapping.name.includes('Store')) return 'stores';
    if (mapping.name.includes('Doctor')) return 'doctors';
    if (mapping.name.includes('Train')) return 'trains';
    return 'stores'; // Default
}

// Update map markers
function updateMapMarkers() {
    // Clear existing markers
    searchMarkers.forEach(marker => map.removeLayer(marker));
    searchMarkers = [];
    
    const filteredResults = filterResultsByCategory();
    
    filteredResults.forEach(place => {
        const mapping = getLocationMapping(place);
        const icon = createCustomIcon(mapping.icon, 24);
        
        const marker = L.marker([parseFloat(place.lat), parseFloat(place.lon)], { 
            icon: icon 
        }).addTo(map);
        
        marker.bindPopup(`
            <div class="text-center">
                <h3 class="font-bold uppercase tracking-wide text-base mb-2">${mapping.name}</h3>
                <p class="text-sm font-medium">${place.display_name.split(',')[0]}</p>
                <p class="text-xs opacity-70 mt-1">${place.display_name.split(',').slice(1, 3).join(',')}</p>
            </div>
        `);
        
        searchMarkers.push(marker);
    });
}

// Enhanced setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');

    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // Filter checkboxes
    const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSearchResults();
            updateMapMarkers();
        });
    });

    // Quick action buttons
    const centerMapBtn = document.getElementById('center-map');
    const toggleLabelsBtn = document.getElementById('toggle-labels');

    if (centerMapBtn) {
        centerMapBtn.addEventListener('click', function() {
            const bethnalGreenCenter = [51.5273, -0.0556];
            map.setView(bethnalGreenCenter, 16);

            // Add visual feedback
            const flash = L.circle(bethnalGreenCenter, {
                radius: 200,
                color: '#FFD700',
                weight: 3,
                opacity: 1,
                fillOpacity: 0.3
            }).addTo(map);

            setTimeout(() => map.removeLayer(flash), 2000);
        });
    }

    if (toggleLabelsBtn) {
        let labelsVisible = true;
        toggleLabelsBtn.addEventListener('click', function() {
            labelsVisible = !labelsVisible;

            map.eachLayer(layer => {
                if (layer.options && layer.options.icon && layer.options.icon.options.className) {
                    const className = layer.options.icon.options.className;
                    if (className.includes('zoom-dependent')) {
                        layer.getElement().style.display = labelsVisible ? 'block' : 'none';
                    }
                }
            });

            toggleLabelsBtn.textContent = labelsVisible ? 'Hide All Labels' : 'Show All Labels';
        });
    }

    // Measure tool functionality
    setTimeout(() => {
        const measureBtn = document.getElementById('measure-btn');
        if (measureBtn) {
            let measuring = false;
            let measurePoints = [];
            let measureLine = null;

            measureBtn.addEventListener('click', function() {
                measuring = !measuring;
                measureBtn.textContent = measuring ? 'Stop Measuring' : 'Measure Distance';
                measureBtn.style.background = measuring ? '#FF6B6B' : 'var(--leather)';

                if (!measuring && measureLine) {
                    map.removeLayer(measureLine);
                    measurePoints = [];
                }
            });

            map.on('click', function(e) {
                if (measuring) {
                    measurePoints.push(e.latlng);

                    if (measurePoints.length === 2) {
                        const distance = measurePoints[0].distanceTo(measurePoints[1]);

                        measureLine = L.polyline(measurePoints, {
                            color: '#FFD700',
                            weight: 4,
                            opacity: 0.8,
                            dashArray: '10, 5'
                        }).addTo(map);

                        const midPoint = [
                            (measurePoints[0].lat + measurePoints[1].lat) / 2,
                            (measurePoints[0].lng + measurePoints[1].lng) / 2
                        ];

                        L.popup()
                            .setLatLng(midPoint)
                            .setContent(`
                                <div class="text-center">
                                    <h4 class="font-bold">Distance Measured</h4>
                                    <p>${distance.toFixed(0)} meters</p>
                                    <p>${(distance * 3.28084).toFixed(0)} feet</p>
                                </div>
                            `)
                            .openOn(map);

                        measuring = false;
                        measureBtn.textContent = 'Measure Distance';
                        measureBtn.style.background = 'var(--leather)';
                        measurePoints = [];
                    }
                }
            });
        }
    }, 1000);
}

// Perform search
async function performSearch() {
    const query = document.getElementById('search-input').value.trim();
    if (!query) return;
    
    const center = map.getCenter();
    await searchNearbyPlaces(center.lat, center.lng, query);
}

// Create ornate RDR2-style compass rose SVG
function createCompassRose() {
    const compassContainer = document.getElementById('compass-rose');
    compassContainer.innerHTML = `
        <svg viewBox="0 0 120 120" class="w-full h-full" style="filter: drop-shadow(3px 3px 6px rgba(58,46,36,0.8));">
            <defs>
                <style>
                    .compass-text {
                        font-family: 'IM Fell English', serif;
                        font-size: 12px;
                        font-weight: bold;
                        fill: ${RDR2_COLORS.road};
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                    }
                    .compass-line {
                        stroke: ${RDR2_COLORS.road};
                        stroke-width: 2.5;
                        stroke-linecap: round;
                    }
                    .compass-point {
                        fill: ${RDR2_COLORS.road};
                        stroke: ${RDR2_COLORS.ink};
                        stroke-width: 0.5;
                    }
                    .compass-ornate {
                        fill: none;
                        stroke: ${RDR2_COLORS.road};
                        stroke-width: 1.5;
                        opacity: 0.8;
                    }
                </style>
            </defs>

            <!-- Ornate outer rings -->
            <circle cx="60" cy="60" r="55" fill="none" class="compass-line" opacity="0.6"/>
            <circle cx="60" cy="60" r="50" fill="none" class="compass-ornate"/>
            <circle cx="60" cy="60" r="45" fill="none" class="compass-ornate"/>

            <!-- Decorative corner flourishes -->
            <path d="M 15 15 Q 20 10 25 15 Q 20 20 15 15" class="compass-ornate"/>
            <path d="M 105 15 Q 100 10 95 15 Q 100 20 105 15" class="compass-ornate"/>
            <path d="M 15 105 Q 20 100 25 105 Q 20 110 15 105" class="compass-ornate"/>
            <path d="M 105 105 Q 100 100 95 105 Q 100 110 105 105" class="compass-ornate"/>

            <!-- Main compass points with ornate design -->
            <polygon points="60,5 65,25 60,20 55,25" class="compass-point"/>
            <polygon points="60,115 65,95 60,100 55,95" class="compass-point"/>
            <polygon points="5,60 25,55 20,60 25,65" class="compass-point"/>
            <polygon points="115,60 95,55 100,60 95,65" class="compass-point"/>

            <!-- Diagonal ornate points -->
            <polygon points="25,25 35,30 30,35 25,30" class="compass-point" opacity="0.8"/>
            <polygon points="95,25 90,30 95,35 100,30" class="compass-point" opacity="0.8"/>
            <polygon points="25,95 30,85 35,90 30,95" class="compass-point" opacity="0.8"/>
            <polygon points="95,95 90,90 95,85 100,90" class="compass-point" opacity="0.8"/>

            <!-- Direction labels with ornate styling -->
            <text x="60" y="18" text-anchor="middle" class="compass-text">NORTH</text>
            <text x="60" y="110" text-anchor="middle" class="compass-text">SOUTH</text>
            <text x="18" y="65" text-anchor="middle" class="compass-text" transform="rotate(-90 18 65)">WEST</text>
            <text x="102" y="65" text-anchor="middle" class="compass-text" transform="rotate(90 102 65)">EAST</text>

            <!-- Center ornament with RDR2 styling -->
            <circle cx="60" cy="60" r="8" class="compass-point"/>
            <circle cx="60" cy="60" r="5" fill="${RDR2_COLORS.parchment}" stroke="${RDR2_COLORS.road}" stroke-width="1"/>
            <circle cx="60" cy="60" r="2" class="compass-point"/>

            <!-- Decorative inner lines -->
            <line x1="60" y1="25" x2="60" y2="35" class="compass-ornate"/>
            <line x1="60" y1="85" x2="60" y2="95" class="compass-ornate"/>
            <line x1="25" y1="60" x2="35" y2="60" class="compass-ornate"/>
            <line x1="85" y1="60" x2="95" y2="60" class="compass-ornate"/>
        </svg>
    `;
}

// Setup parchment overlay
function setupParchmentOverlay() {
    // Create a simple parchment pattern using CSS if image is not available
    const overlay = document.getElementById('parchment-overlay');
    overlay.style.backgroundImage = `
        radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, rgba(210, 180, 140, 0.05) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(210, 180, 140, 0.05) 25%, transparent 25%)
    `;
}

// Create comprehensive hand-drawn Bethnal Green map overlays
function createCustomBethnalGreenMap() {
    console.log('Creating custom Bethnal Green map overlays...');

    // Set map background to parchment
    map.getContainer().style.backgroundColor = RDR2_COLORS.parchment;

    // Comprehensive Bethnal Green features covering the entire area
    const bethnalGreenFeatures = {
        // Complete road network
        roads: [
            // Major arterial roads
            { name: "Roman Road", path: [[51.5250, -0.0680], [51.5280, -0.0400]], type: "major" },
            { name: "Bethnal Green Road", path: [[51.5190, -0.0700], [51.5220, -0.0400]], type: "major" },
            { name: "Cambridge Heath Road", path: [[51.5160, -0.0550], [51.5380, -0.0520]], type: "major" },
            { name: "Old Ford Road", path: [[51.5300, -0.0680], [51.5330, -0.0400]], type: "major" },
            { name: "Mile End Road", path: [[51.5150, -0.0650], [51.5180, -0.0450]], type: "major" },

            // Secondary roads
            { name: "Globe Road", path: [[51.5240, -0.0620], [51.5260, -0.0480]], type: "minor" },
            { name: "Hackney Road", path: [[51.5280, -0.0680], [51.5300, -0.0500]], type: "minor" },
            { name: "Vallance Road", path: [[51.5200, -0.0620], [51.5240, -0.0580]], type: "minor" },
            { name: "Brady Street", path: [[51.5210, -0.0590], [51.5230, -0.0550]], type: "minor" },
            { name: "Whitechapel Road", path: [[51.5150, -0.0680], [51.5170, -0.0500]], type: "minor" },
            { name: "Commercial Street", path: [[51.5180, -0.0620], [51.5220, -0.0580]], type: "minor" },
            { name: "Brick Lane", path: [[51.5200, -0.0710], [51.5250, -0.0680]], type: "minor" },
            { name: "Columbia Road", path: [[51.5290, -0.0650], [51.5310, -0.0600]], type: "minor" },
            { name: "Gosset Street", path: [[51.5260, -0.0580], [51.5280, -0.0540]], type: "minor" },
            { name: "Approach Road", path: [[51.5250, -0.0570], [51.5270, -0.0530]], type: "minor" }
        ],

        // All parks and green spaces
        parks: [
            { name: "Victoria Park", center: [51.5340, -0.0430], radius: 0.004 },
            { name: "Bethnal Green Gardens", center: [51.5270, -0.0560], radius: 0.0015 },
            { name: "Weavers Fields", center: [51.5200, -0.0520], radius: 0.001 },
            { name: "Allen Gardens", center: [51.5220, -0.0640], radius: 0.0008 },
            { name: "Meath Gardens", center: [51.5320, -0.0580], radius: 0.0012 },
            { name: "Jesus Green", center: [51.5180, -0.0600], radius: 0.0006 },
            { name: "Barmy Park", center: [51.5300, -0.0460], radius: 0.0008 }
        ],

        // Comprehensive landmarks and buildings
        landmarks: [
            // Transport
            { name: "Bethnal Green Station", pos: [51.5270, -0.0550], type: "station" },
            { name: "Cambridge Heath Station", pos: [51.5310, -0.0520], type: "station" },
            { name: "Mile End Station", pos: [51.5250, -0.0330], type: "station" },
            { name: "Stepney Green Station", pos: [51.5170, -0.0470], type: "station" },

            // Pubs and establishments
            { name: "The Blind Beggar", pos: [51.5200, -0.0650], type: "pub" },
            { name: "The Crown", pos: [51.5280, -0.0590], type: "pub" },
            { name: "The Approach Tavern", pos: [51.5260, -0.0540], type: "pub" },
            { name: "The Camel", pos: [51.5240, -0.0620], type: "pub" },
            { name: "The Palm Tree", pos: [51.5300, -0.0460], type: "pub" },
            { name: "The Royal Oak", pos: [51.5220, -0.0580], type: "pub" },

            // Cultural and civic buildings
            { name: "York Hall", pos: [51.5250, -0.0580], type: "building" },
            { name: "Bethnal Green Museum", pos: [51.5240, -0.0570], type: "museum" },
            { name: "Rich Mix", pos: [51.5200, -0.0710], type: "theatre" },
            { name: "Bethnal Green Library", pos: [51.5260, -0.0560], type: "library" },
            { name: "Town Hall", pos: [51.5230, -0.0590], type: "building" },
            { name: "St. John's Church", pos: [51.5280, -0.0570], type: "church" },
            { name: "Christ Church", pos: [51.5210, -0.0640], type: "church" },

            // Markets and shopping
            { name: "Roman Road Market", pos: [51.5260, -0.0550], type: "market" },
            { name: "Columbia Road Market", pos: [51.5300, -0.0630], type: "market" },
            { name: "Brick Lane Market", pos: [51.5220, -0.0700], type: "market" },

            // Schools and institutions
            { name: "Bethnal Green Academy", pos: [51.5290, -0.0600], type: "school" },
            { name: "Morpeth School", pos: [51.5250, -0.0520], type: "school" },
            { name: "Royal London Hospital", pos: [51.5180, -0.0590], type: "hospital" },

            // Housing estates
            { name: "Cranbrook Estate", pos: [51.5270, -0.0520], type: "estate" },
            { name: "Collingwood Estate", pos: [51.5240, -0.0500], type: "estate" },
            { name: "Dorset Estate", pos: [51.5200, -0.0580], type: "estate" }
        ],

        // Water features
        water: [
            { name: "Regent's Canal", path: [[51.5330, -0.0500], [51.5350, -0.0400]], width: 0.0008 },
            { name: "Hertford Union Canal", path: [[51.5320, -0.0450], [51.5340, -0.0420]], width: 0.0006 }
        ],

        // Historical sites and points of interest
        historical: [
            { name: "Jack the Ripper Territory", pos: [51.5190, -0.0650], type: "historical" },
            { name: "Old Nichol Slum Site", pos: [51.5210, -0.0710], type: "historical" },
            { name: "Boundary Estate", pos: [51.5220, -0.0680], type: "historical" },
            { name: "Truman Brewery", pos: [51.5200, -0.0720], type: "brewery" },
            { name: "Spitalfields Market", pos: [51.5180, -0.0750], type: "market" },
            { name: "Christ Church Spitalfields", pos: [51.5190, -0.0730], type: "church" }
        ],

        // Terrain features
        terrain: [
            { name: "Mile End Waste", center: [51.5180, -0.0400], radius: 0.002, type: "wasteland" },
            { name: "Bethnal Green Common", center: [51.5250, -0.0530], radius: 0.0015, type: "common" },
            { name: "Stepney Fields", center: [51.5160, -0.0480], radius: 0.0012, type: "fields" }
        ],

        // Street markets and commercial areas
        commercialAreas: [
            { name: "Roman Road Market", bounds: [[51.5255, -0.0570], [51.5265, -0.0530]], type: "market" },
            { name: "Columbia Road Flower Market", bounds: [[51.5295, -0.0640], [51.5305, -0.0620]], type: "flower_market" },
            { name: "Brick Lane Curry Mile", bounds: [[51.5200, -0.0720], [51.5240, -0.0700]], type: "restaurant_strip" },
            { name: "Bethnal Green High Street", bounds: [[51.5260, -0.0580], [51.5280, -0.0540]], type: "high_street" }
        ]
    };

    // Draw comprehensive road network
    bethnalGreenFeatures.roads.forEach(road => {
        const roadStyle = road.type === 'major' ? {
            color: RDR2_COLORS.road,
            weight: 10,
            opacity: 0.9,
            dashArray: '3, 2',
            lineCap: 'round',
            lineJoin: 'round'
        } : {
            color: RDR2_COLORS.road,
            weight: 6,
            opacity: 0.7,
            dashArray: '2, 1',
            lineCap: 'round'
        };

        const roadLine = L.polyline(road.path, roadStyle).addTo(map);

        // Add road name label at midpoint
        const midPoint = road.path[Math.floor(road.path.length / 2)];
        addTextLabel(road.name, midPoint, {
            fontSize: road.type === 'major' ? '14px' : '12px',
            minZoom: road.type === 'major' ? 15 : 16,
            className: 'road-label',
            rotation: calculateRoadAngle(road.path)
        });
    });

    // Draw parks and green spaces
    bethnalGreenFeatures.parks.forEach(park => {
        const parkCircle = L.circle(park.center, {
            radius: park.radius * 111000, // Convert to meters
            color: RDR2_COLORS.icon,
            weight: 3,
            opacity: 0.8,
            fillColor: '#8FBC8F',
            fillOpacity: 0.5,
            dashArray: '4, 2'
        }).addTo(map);

        // Add park name label
        addTextLabel(park.name, park.center, {
            fontSize: park.name === 'Victoria Park' ? '16px' : '14px',
            minZoom: 15,
            className: 'park-label',
            color: RDR2_COLORS.icon
        });
    });

    // Draw water features
    bethnalGreenFeatures.water.forEach(water => {
        const waterLine = L.polyline(water.path, {
            color: RDR2_COLORS.water,
            weight: 15,
            opacity: 0.8,
            lineCap: 'round'
        }).addTo(map);

        // Add water feature label
        const midPoint = water.path[Math.floor(water.path.length / 2)];
        addTextLabel(water.name, midPoint, {
            fontSize: '13px',
            minZoom: 15,
            className: 'water-label',
            color: RDR2_COLORS.waterDark,
            rotation: calculateRoadAngle(water.path)
        });
    });

    // Add landmarks with custom icons and labels
    bethnalGreenFeatures.landmarks.forEach(landmark => {
        const iconType = getIconType(landmark.type);
        const icon = createCustomIcon(iconType, 28);
        const marker = L.marker(landmark.pos, { icon: icon }).addTo(map);

        // Add landmark name label with appropriate zoom level
        const minZoom = getLabelMinZoom(landmark.type);
        addTextLabel(landmark.name, landmark.pos, {
            fontSize: getFontSize(landmark.type),
            minZoom: minZoom,
            className: `${landmark.type}-label`,
            color: RDR2_COLORS.road,
            offset: [0, -35] // Position above the icon
        });
    });

    // Draw historical sites
    bethnalGreenFeatures.historical.forEach(site => {
        const iconType = site.type === 'historical' ? 'x' :
                        site.type === 'brewery' ? 'stew' :
                        site.type === 'church' ? 'pillars' : 'tshirt';

        const icon = createCustomIcon(iconType, 24);
        const marker = L.marker(site.pos, {
            icon: icon,
            opacity: 0.8
        }).addTo(map);

        addTextLabel(site.name, site.pos, {
            fontSize: '12px',
            minZoom: 17,
            className: 'historical-label',
            color: '#8B4513',
            offset: [0, -30]
        });
    });

    // Draw terrain features
    bethnalGreenFeatures.terrain.forEach(terrain => {
        const terrainColor = terrain.type === 'wasteland' ? '#D2B48C' :
                           terrain.type === 'common' ? '#9ACD32' : '#98FB98';

        const terrainCircle = L.circle(terrain.center, {
            radius: terrain.radius * 111000,
            color: RDR2_COLORS.icon,
            weight: 2,
            opacity: 0.6,
            fillColor: terrainColor,
            fillOpacity: 0.3,
            dashArray: '5, 3'
        }).addTo(map);

        addTextLabel(terrain.name, terrain.center, {
            fontSize: '13px',
            minZoom: 16,
            className: 'terrain-label',
            color: RDR2_COLORS.icon
        });
    });

    // Draw commercial areas
    bethnalGreenFeatures.commercialAreas.forEach(area => {
        const areaColor = area.type === 'market' ? '#FFD700' :
                         area.type === 'flower_market' ? '#FF69B4' :
                         area.type === 'restaurant_strip' ? '#FF6347' : '#DDA0DD';

        const areaRect = L.rectangle(area.bounds, {
            color: RDR2_COLORS.road,
            weight: 2,
            opacity: 0.7,
            fillColor: areaColor,
            fillOpacity: 0.2,
            dashArray: '3, 3'
        }).addTo(map);

        const center = [
            (area.bounds[0][0] + area.bounds[1][0]) / 2,
            (area.bounds[0][1] + area.bounds[1][1]) / 2
        ];

        addTextLabel(area.name, center, {
            fontSize: '14px',
            minZoom: 16,
            className: 'commercial-label',
            color: RDR2_COLORS.road
        });
    });

    // Add enhanced decorative elements
    addMapDecorations();

    // Add weather and time effects
    addAtmosphericEffects();

    // Add interactive features
    addInteractiveFeatures();

    // Ensure loading screen is hidden after map creation
    console.log('Map creation complete, hiding loading screen');
    setTimeout(() => {
        hideLoading();
    }, 1000);
}

// Add text label that appears at specific zoom levels
function addTextLabel(text, position, options = {}) {
    const defaults = {
        fontSize: '14px',
        minZoom: 16,
        className: 'map-label',
        color: RDR2_COLORS.road,
        offset: [0, 0],
        rotation: 0
    };

    const opts = { ...defaults, ...options };

    const textIcon = L.divIcon({
        className: `${opts.className} zoom-dependent`,
        html: `<span style="
            font-family: 'IM Fell English', serif;
            font-size: ${opts.fontSize};
            font-weight: bold;
            color: ${opts.color};
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7), 1px 1px 2px rgba(255,255,255,0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
            white-space: nowrap;
            transform: rotate(${opts.rotation}deg);
            display: block;
            text-align: center;
        ">${text}</span>`,
        iconSize: [text.length * 8, 20],
        iconAnchor: [text.length * 4, 10]
    });

    const labelMarker = L.marker(position, {
        icon: textIcon,
        zIndexOffset: 1000
    }).addTo(map);

    // Store zoom level info for visibility control
    labelMarker.minZoom = opts.minZoom;

    return labelMarker;
}

// Calculate angle for road/water labels
function calculateRoadAngle(path) {
    if (path.length < 2) return 0;
    const start = path[0];
    const end = path[path.length - 1];
    const angle = Math.atan2(end[0] - start[0], end[1] - start[1]) * 180 / Math.PI;
    return angle;
}

// Get appropriate icon type for landmark
function getIconType(type) {
    const iconMap = {
        'station': 'locomotive',
        'pub': 'stew',
        'museum': 'camera',
        'theatre': 'masks',
        'library': 'camera',
        'church': 'pillars',
        'market': 'tshirt',
        'school': 'pillars',
        'hospital': 'cross',
        'estate': 'bed',
        'building': 'pillars'
    };
    return iconMap[type] || 'x';
}

// Get font size based on landmark importance
function getFontSize(type) {
    const sizeMap = {
        'station': '15px',
        'pub': '13px',
        'museum': '14px',
        'theatre': '14px',
        'library': '13px',
        'church': '13px',
        'market': '14px',
        'school': '12px',
        'hospital': '14px',
        'estate': '11px',
        'building': '13px'
    };
    return sizeMap[type] || '12px';
}

// Get minimum zoom level for label visibility
function getLabelMinZoom(type) {
    const zoomMap = {
        'station': 15,
        'pub': 16,
        'museum': 16,
        'theatre': 16,
        'library': 17,
        'church': 16,
        'market': 15,
        'school': 17,
        'hospital': 15,
        'estate': 17,
        'building': 16
    };
    return zoomMap[type] || 16;
}

// Add decorative map elements and zoom control
function addMapDecorations() {
    // Add territory title and compass directions
    const decorations = [
        { text: "NORTH", pos: [51.5390, -0.0550], size: "14px", minZoom: 14 },
        { text: "BETHNAL GREEN TERRITORY", pos: [51.5350, -0.0550], size: "20px", minZoom: 14 },
        { text: "EAST LONDON FRONTIER", pos: [51.5160, -0.0550], size: "12px", minZoom: 15 },
        { text: "TOWER HAMLETS", pos: [51.5180, -0.0450], size: "10px", minZoom: 16 }
    ];

    decorations.forEach(decoration => {
        addTextLabel(decoration.text, decoration.pos, {
            fontSize: decoration.size,
            minZoom: decoration.minZoom,
            className: 'decoration-label',
            color: RDR2_COLORS.road
        });
    });

    // Add zoom-based label visibility control
    map.on('zoomend', updateLabelVisibility);
    updateLabelVisibility(); // Initial call
}

// Control label visibility based on zoom level
function updateLabelVisibility() {
    const currentZoom = map.getZoom();

    map.eachLayer(layer => {
        if (layer.options && layer.options.icon && layer.options.icon.options.className) {
            const className = layer.options.icon.options.className;
            if (className.includes('zoom-dependent') && layer.minZoom) {
                if (currentZoom >= layer.minZoom) {
                    layer.getElement().style.display = 'block';
                } else {
                    layer.getElement().style.display = 'none';
                }
            }
        }
    });
}

// Add atmospheric effects for immersion
function addAtmosphericEffects() {
    // Add subtle fog/mist effect
    const fogOverlay = L.rectangle([
        [51.5140, -0.0720],
        [51.5420, -0.0380]
    ], {
        color: 'transparent',
        fillColor: '#F5F5DC',
        fillOpacity: 0.05,
        interactive: false
    }).addTo(map);

    // Add time-based lighting (subtle day/night cycle)
    const hour = new Date().getHours();
    const isEvening = hour >= 18 || hour <= 6;

    if (isEvening) {
        const nightOverlay = L.rectangle([
            [51.5140, -0.0720],
            [51.5420, -0.0380]
        ], {
            color: 'transparent',
            fillColor: '#191970',
            fillOpacity: 0.1,
            interactive: false
        }).addTo(map);
    }

    // Add wind direction indicator
    const windDirection = Math.floor(Math.random() * 8); // Random wind direction
    const windDirections = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];

    addTextLabel(`Wind: ${windDirections[windDirection]}`, [51.5400, -0.0400], {
        fontSize: '10px',
        minZoom: 14,
        className: 'weather-label',
        color: RDR2_COLORS.road
    });
}

// Add interactive features
function addInteractiveFeatures() {
    // Add distance measurement tool
    let measurementMode = false;
    let measurementPoints = [];

    // Add custom control for measurement
    const measureControl = L.control({ position: 'topright' });
    measureControl.onAdd = function(map) {
        const div = L.DomUtil.create('div', 'measure-control');
        div.innerHTML = '<button id="measure-btn" style="background: var(--leather); color: var(--parchment); border: 2px solid var(--road); padding: 8px; font-family: \'IM Fell English\', serif; font-weight: bold;">Measure Distance</button>';
        return div;
    };
    measureControl.addTo(map);

    // Add exploration tracking
    const exploredAreas = new Set();

    map.on('moveend', function() {
        const center = map.getCenter();
        const areaKey = `${Math.floor(center.lat * 1000)}_${Math.floor(center.lng * 1000)}`;

        if (!exploredAreas.has(areaKey)) {
            exploredAreas.add(areaKey);
            updateExplorationProgress();
        }
    });

    // Add right-click context menu
    map.on('contextmenu', function(e) {
        const popup = L.popup()
            .setLatLng(e.latlng)
            .setContent(`
                <div class="context-menu">
                    <h4 class="font-bold">Territory Coordinates</h4>
                    <p>Lat: ${e.latlng.lat.toFixed(4)}</p>
                    <p>Lng: ${e.latlng.lng.toFixed(4)}</p>
                    <button onclick="addCustomMarker(${e.latlng.lat}, ${e.latlng.lng})"
                            style="background: var(--parchment); border: 1px solid var(--road); padding: 4px 8px; margin-top: 8px; font-family: 'IM Fell English', serif;">
                        Mark Location
                    </button>
                </div>
            `)
            .openOn(map);
    });
}

// Update exploration progress
function updateExplorationProgress() {
    const progressElement = document.getElementById('exploration-progress');
    if (progressElement) {
        const totalAreas = 100; // Approximate total explorable areas
        const exploredCount = exploredAreas.size;
        const percentage = Math.min(100, (exploredCount / totalAreas) * 100);
        progressElement.textContent = `Territory Explored: ${percentage.toFixed(1)}%`;
    }
}

// Add custom marker function
function addCustomMarker(lat, lng) {
    const customIcon = createCustomIcon('star', 20);
    const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);

    marker.bindPopup(`
        <div class="text-center">
            <h3 class="font-bold uppercase">Custom Waypoint</h3>
            <p class="text-sm">Marked Location</p>
            <button onclick="removeMarker(this)" style="background: var(--road); color: var(--parchment); border: none; padding: 4px 8px; margin-top: 4px; font-family: 'IM Fell English', serif;">Remove</button>
        </div>
    `);

    // Store marker for potential removal
    if (!window.customMarkers) window.customMarkers = [];
    window.customMarkers.push(marker);
}

// Remove custom marker
function removeMarker(button) {
    // This would be implemented to remove the specific marker
    map.closePopup();
}

// Hide loading screen
function hideLoading() {
    const loading = document.getElementById('loading');
    loading.style.opacity = '0';
    setTimeout(() => {
        loading.style.display = 'none';
    }, 500);
}
