# fragment-cache [![NPM version](https://img.shields.io/npm/v/fragment-cache.svg?style=flat)](https://www.npmjs.com/package/fragment-cache) [![NPM downloads](https://img.shields.io/npm/dm/fragment-cache.svg?style=flat)](https://npmjs.org/package/fragment-cache) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/fragment-cache.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/fragment-cache)

> A cache for managing namespaced sub-caches

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save fragment-cache
```

## Usage

```js
var Fragment = require('fragment-cache');
var fragment = new Fragment();
```

## API

### [FragmentCache](index.js#L24)

Create a new `FragmentCache` with an optional object to use for `caches`.

**Example**

```js
var fragment = new FragmentCache();
```

**Params**

* `cacheName` **{String}**
* `returns` **{Object}**: Returns the [map-cache](https://github.com/jonschlinkert/map-cache) instance.

### [.cache](index.js#L49)

Get cache `name` from the `fragment.caches` object. Creates a new `MapCache` if it doesn't already exist.

**Example**

```js
var cache = fragment.cache('files');
console.log(fragment.caches.hasOwnProperty('files'));
//=> true
```

**Params**

* `cacheName` **{String}**
* `returns` **{Object}**: Returns the [map-cache](https://github.com/jonschlinkert/map-cache) instance.

### [.set](index.js#L67)

Set a value for property `key` on cache `name`

**Example**

```js
fragment.set('files', 'somefile.js', new File({path: 'somefile.js'}));
```

**Params**

* `name` **{String}**
* `key` **{String}**: Property name to set
* `val` **{any}**: The value of `key`
* `returns` **{Object}**: The cache instance for chaining

### [.has](index.js#L93)

Returns true if a non-undefined value is set for `key` on fragment cache `name`.

**Example**

```js
var cache = fragment.cache('files');
cache.set('somefile.js');

console.log(cache.has('somefile.js'));
//=> true

console.log(cache.has('some-other-file.js'));
//=> false
```

**Params**

* `name` **{String}**: Cache name
* `key` **{String}**: Optionally specify a property to check for on cache `name`
* `returns` **{Boolean}**

### [.get](index.js#L115)

Get `name`, or if specified, the value of `key`. Invokes the [cache](#cache) method, so that cache `name` will be created it doesn't already exist. If `key` is not passed, the entire cache (`name`) is returned.

**Example**

```js
var Vinyl = require('vinyl');
var cache = fragment.cache('files');
cache.set('somefile.js', new Vinyl({path: 'somefile.js'}));
console.log(cache.get('somefile.js'));
//=> <File "somefile.js">
```

**Params**

* `name` **{String}**
* `returns` **{Object}**: Returns cache `name`, or the value of `key` if specified

## About

### Related projects

* [base](https://www.npmjs.com/package/base): base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting… [more](https://github.com/node-base/base) | [homepage](https://github.com/node-base/base "base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.")
* [map-cache](https://www.npmjs.com/package/map-cache): Basic cache object for storing key-value pairs. | [homepage](https://github.com/jonschlinkert/map-cache "Basic cache object for storing key-value pairs.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/fragment-cache/blob/master/LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on October 17, 2016._