{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "prop", "key", "value", "t", "objectProperty", "identifier", "method", "body", "objectMethod", "blockStatement", "takeDecorators", "node", "result", "decorators", "length", "arrayExpression", "map", "decorator", "expression", "undefined", "<PERSON><PERSON><PERSON>", "computed", "isIdentifier", "stringLiteral", "name", "String", "extractElementDescriptor", "file", "classRef", "superRef", "path", "isMethod", "isClassMethod", "isPrivate", "buildCodeFrameError", "type", "scope", "isTSDeclareMethod", "ReplaceSupers", "methodPath", "objectRef", "refToPreserve", "replace", "properties", "kind", "static", "booleanLiteral", "filter", "Boolean", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "push", "toExpression", "isClassProperty", "template", "statements", "ast", "buildUndefinedNode", "remove", "objectExpression", "addDecorateHelper", "addHelper", "buildDecoratedClass", "ref", "elements", "initializeId", "generateUidIdentifier", "isDeclaration", "id", "isStrict", "isInStrictMode", "superClass", "cloneNode", "superId", "generateUidIdentifierBasedOnNode", "classDecorators", "definitions", "element", "abstract", "wrapperCall", "nullLiteral", "arguments", "directives", "directive", "directiveLiteral", "replacement", "classPathDesc", "statement", "instanceNodes", "wrapClass", "replaceWith", "get"], "sources": ["../src/decorators-2018-09.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\nif (process.env.BABEL_8_BREAKING && process.env.IS_PUBLISH) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nimport { types as t, template } from \"@babel/core\";\nimport type { File, NodePath } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\n\ntype Decoratable = Extract<t.Node, { decorators?: t.Decorator[] | null }>;\n\nfunction prop(key: string, value?: t.Expression) {\n  if (!value) return null;\n  return t.objectProperty(t.identifier(key), value);\n}\n\nfunction method(key: string, body: t.Statement[]) {\n  return t.objectMethod(\n    \"method\",\n    t.identifier(key),\n    [],\n    t.blockStatement(body),\n  );\n}\n\nfunction takeDecorators(node: Decoratable) {\n  let result: t.ArrayExpression | undefined;\n  if (node.decorators && node.decorators.length > 0) {\n    result = t.arrayExpression(\n      node.decorators.map(decorator => decorator.expression),\n    );\n  }\n  node.decorators = undefined;\n  return result;\n}\n\ntype AcceptedElement = Exclude<ClassElement, t.TSIndexSignature>;\ntype SupportedElement = Exclude<\n  AcceptedElement,\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty\n  | t.StaticBlock\n>;\n\nfunction getKey(node: SupportedElement) {\n  if (node.computed) {\n    return node.key;\n  } else if (t.isIdentifier(node.key)) {\n    return t.stringLiteral(node.key.name);\n  } else {\n    return t.stringLiteral(\n      String(\n        // A non-identifier non-computed key\n        (node.key as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n          .value,\n      ),\n    );\n  }\n}\n\nfunction extractElementDescriptor(\n  file: File,\n  classRef: t.Identifier,\n  superRef: t.Identifier,\n  path: NodePath<AcceptedElement>,\n) {\n  const isMethod = path.isClassMethod();\n  if (path.isPrivate()) {\n    throw path.buildCodeFrameError(\n      `Private ${\n        isMethod ? \"methods\" : \"fields\"\n      } in decorated classes are not supported yet.`,\n    );\n  }\n  if (path.node.type === \"ClassAccessorProperty\") {\n    throw path.buildCodeFrameError(\n      `Accessor properties are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n  if (path.node.type === \"StaticBlock\") {\n    throw path.buildCodeFrameError(\n      `Static blocks are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n\n  const { node, scope } = path as NodePath<SupportedElement>;\n\n  if (!path.isTSDeclareMethod()) {\n    new ReplaceSupers({\n      methodPath: path as NodePath<\n        Exclude<SupportedElement, t.TSDeclareMethod>\n      >,\n      objectRef: classRef,\n      superRef,\n      file,\n      refToPreserve: classRef,\n    }).replace();\n  }\n\n  const properties: t.ObjectExpression[\"properties\"] = [\n    prop(\"kind\", t.stringLiteral(t.isClassMethod(node) ? node.kind : \"field\")),\n    prop(\"decorators\", takeDecorators(node as Decoratable)),\n    prop(\"static\", node.static && t.booleanLiteral(true)),\n    prop(\"key\", getKey(node)),\n  ].filter(Boolean);\n\n  if (isMethod) {\n    if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n      // polyfill when being run by an older Babel version\n      path.ensureFunctionName ??=\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n    }\n    // @ts-expect-error path is a ClassMethod, that technically\n    // is not supported as it does not have an .id property\n    // This plugin will however then transform the ClassMethod\n    // to a function expression, so it's fine.\n    path.ensureFunctionName(false);\n\n    properties.push(prop(\"value\", t.toExpression(path.node)));\n  } else if (t.isClassProperty(node) && node.value) {\n    properties.push(\n      method(\"value\", template.statements.ast`return ${node.value}`),\n    );\n  } else {\n    properties.push(prop(\"value\", scope.buildUndefinedNode()));\n  }\n\n  path.remove();\n\n  return t.objectExpression(properties);\n}\n\nfunction addDecorateHelper(file: File) {\n  return file.addHelper(\"decorate\");\n}\n\ntype ClassElement = t.Class[\"body\"][\"body\"][number];\ntype ClassElementPath = NodePath<ClassElement>;\n\nexport function buildDecoratedClass(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  elements: ClassElementPath[],\n  file: File,\n) {\n  const { node, scope } = path;\n  const initializeId = scope.generateUidIdentifier(\"initialize\");\n  const isDeclaration = node.id && path.isDeclaration();\n  const isStrict = path.isInStrictMode();\n  const { superClass } = node;\n\n  node.type = \"ClassDeclaration\";\n  if (!node.id) node.id = t.cloneNode(ref);\n\n  let superId: t.Identifier;\n  if (superClass) {\n    superId = scope.generateUidIdentifierBasedOnNode(node.superClass, \"super\");\n    node.superClass = superId;\n  }\n\n  const classDecorators = takeDecorators(node);\n  const definitions = t.arrayExpression(\n    elements\n      .filter(\n        element =>\n          // @ts-expect-error Ignore TypeScript's abstract methods (see #10514)\n          !element.node.abstract && element.node.type !== \"TSIndexSignature\",\n      )\n      .map(path =>\n        extractElementDescriptor(\n          file,\n          node.id,\n          superId,\n          // @ts-expect-error TS can not exclude TSIndexSignature\n          path,\n        ),\n      ),\n  );\n\n  const wrapperCall = template.expression.ast`\n    ${addDecorateHelper(file)}(\n      ${classDecorators || t.nullLiteral()},\n      function (${initializeId}, ${superClass ? t.cloneNode(superId) : null}) {\n        ${node}\n        return { F: ${t.cloneNode(node.id)}, d: ${definitions} };\n      },\n      ${superClass}\n    )\n  ` as t.CallExpression & { arguments: [unknown, t.FunctionExpression] };\n\n  if (!isStrict) {\n    wrapperCall.arguments[1].body.directives.push(\n      t.directive(t.directiveLiteral(\"use strict\")),\n    );\n  }\n\n  let replacement: t.Node = wrapperCall;\n  let classPathDesc = \"arguments.1.body.body.0\";\n  if (isDeclaration) {\n    replacement = template.statement.ast`let ${ref} = ${wrapperCall}`;\n    classPathDesc = \"declarations.0.init.\" + classPathDesc;\n  }\n\n  return {\n    instanceNodes: [\n      template.statement.ast`\n        ${t.cloneNode(initializeId)}(this)\n      ` as t.ExpressionStatement,\n    ],\n    wrapClass(path: NodePath<t.Class>) {\n      path.replaceWith(replacement);\n      return path.get(classPathDesc) as NodePath;\n    },\n  };\n}\n"], "mappings": ";;;;;;AAOA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,oBAAA,GAAAD,OAAA;AAAyD;AAIzD,SAASE,IAAIA,CAACC,GAAW,EAAEC,KAAoB,EAAE;EAC/C,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,OAAOC,WAAC,CAACC,cAAc,CAACD,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EAAEC,KAAK,CAAC;AACnD;AAEA,SAASI,MAAMA,CAACL,GAAW,EAAEM,IAAmB,EAAE;EAChD,OAAOJ,WAAC,CAACK,YAAY,CACnB,QAAQ,EACRL,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EACjB,EAAE,EACFE,WAAC,CAACM,cAAc,CAACF,IAAI,CACvB,CAAC;AACH;AAEA,SAASG,cAAcA,CAACC,IAAiB,EAAE;EACzC,IAAIC,MAAqC;EACzC,IAAID,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACE,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDF,MAAM,GAAGT,WAAC,CAACY,eAAe,CACxBJ,IAAI,CAACE,UAAU,CAACG,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACC,UAAU,CACvD,CAAC;EACH;EACAP,IAAI,CAACE,UAAU,GAAGM,SAAS;EAC3B,OAAOP,MAAM;AACf;AAWA,SAASQ,MAAMA,CAACT,IAAsB,EAAE;EACtC,IAAIA,IAAI,CAACU,QAAQ,EAAE;IACjB,OAAOV,IAAI,CAACV,GAAG;EACjB,CAAC,MAAM,IAAIE,WAAC,CAACmB,YAAY,CAACX,IAAI,CAACV,GAAG,CAAC,EAAE;IACnC,OAAOE,WAAC,CAACoB,aAAa,CAACZ,IAAI,CAACV,GAAG,CAACuB,IAAI,CAAC;EACvC,CAAC,MAAM;IACL,OAAOrB,WAAC,CAACoB,aAAa,CACpBE,MAAM,CAEHd,IAAI,CAACV,GAAG,CACNC,KACL,CACF,CAAC;EACH;AACF;AAEA,SAASwB,wBAAwBA,CAC/BC,IAAU,EACVC,QAAsB,EACtBC,QAAsB,EACtBC,IAA+B,EAC/B;EACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAa,CAAC,CAAC;EACrC,IAAIF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE;IACpB,MAAMH,IAAI,CAACI,mBAAmB,CAC5B,WACEH,QAAQ,GAAG,SAAS,GAAG,QAAQ,8CAEnC,CAAC;EACH;EACA,IAAID,IAAI,CAACnB,IAAI,CAACwB,IAAI,KAAK,uBAAuB,EAAE;IAC9C,MAAML,IAAI,CAACI,mBAAmB,CAC5B,wHACF,CAAC;EACH;EACA,IAAIJ,IAAI,CAACnB,IAAI,CAACwB,IAAI,KAAK,aAAa,EAAE;IACpC,MAAML,IAAI,CAACI,mBAAmB,CAC5B,kHACF,CAAC;EACH;EAEA,MAAM;IAAEvB,IAAI;IAAEyB;EAAM,CAAC,GAAGN,IAAkC;EAE1D,IAAI,CAACA,IAAI,CAACO,iBAAiB,CAAC,CAAC,EAAE;IAC7B,IAAIC,4BAAa,CAAC;MAChBC,UAAU,EAAET,IAEX;MACDU,SAAS,EAAEZ,QAAQ;MACnBC,QAAQ;MACRF,IAAI;MACJc,aAAa,EAAEb;IACjB,CAAC,CAAC,CAACc,OAAO,CAAC,CAAC;EACd;EAEA,MAAMC,UAA4C,GAAG,CACnD3C,IAAI,CAAC,MAAM,EAAEG,WAAC,CAACoB,aAAa,CAACpB,WAAC,CAAC6B,aAAa,CAACrB,IAAI,CAAC,GAAGA,IAAI,CAACiC,IAAI,GAAG,OAAO,CAAC,CAAC,EAC1E5C,IAAI,CAAC,YAAY,EAAEU,cAAc,CAACC,IAAmB,CAAC,CAAC,EACvDX,IAAI,CAAC,QAAQ,EAAEW,IAAI,CAACkC,MAAM,IAAI1C,WAAC,CAAC2C,cAAc,CAAC,IAAI,CAAC,CAAC,EACrD9C,IAAI,CAAC,KAAK,EAAEoB,MAAM,CAACT,IAAI,CAAC,CAAC,CAC1B,CAACoC,MAAM,CAACC,OAAO,CAAC;EAEjB,IAAIjB,QAAQ,EAAE;IACqD;MAAA,IAAAkB,qBAAA;MAE/D,CAAAA,qBAAA,GAAAnB,IAAI,CAACoB,kBAAkB,YAAAD,qBAAA,GAAvBnB,IAAI,CAACoB,kBAAkB,GAErBpD,OAAO,CAAC,iBAAiB,CAAC,CAACqD,QAAQ,CAACC,SAAS,CAACF,kBAAkB;IACpE;IAKApB,IAAI,CAACoB,kBAAkB,CAAC,KAAK,CAAC;IAE9BP,UAAU,CAACU,IAAI,CAACrD,IAAI,CAAC,OAAO,EAAEG,WAAC,CAACmD,YAAY,CAACxB,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC;EAC3D,CAAC,MAAM,IAAIR,WAAC,CAACoD,eAAe,CAAC5C,IAAI,CAAC,IAAIA,IAAI,CAACT,KAAK,EAAE;IAChDyC,UAAU,CAACU,IAAI,CACb/C,MAAM,CAAC,OAAO,EAAEkD,cAAQ,CAACC,UAAU,CAACC,GAAG,UAAU/C,IAAI,CAACT,KAAK,EAAE,CAC/D,CAAC;EACH,CAAC,MAAM;IACLyC,UAAU,CAACU,IAAI,CAACrD,IAAI,CAAC,OAAO,EAAEoC,KAAK,CAACuB,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAC5D;EAEA7B,IAAI,CAAC8B,MAAM,CAAC,CAAC;EAEb,OAAOzD,WAAC,CAAC0D,gBAAgB,CAAClB,UAAU,CAAC;AACvC;AAEA,SAASmB,iBAAiBA,CAACnC,IAAU,EAAE;EACrC,OAAOA,IAAI,CAACoC,SAAS,CAAC,UAAU,CAAC;AACnC;AAKO,SAASC,mBAAmBA,CACjCC,GAAiB,EACjBnC,IAAuB,EACvBoC,QAA4B,EAC5BvC,IAAU,EACV;EACA,MAAM;IAAEhB,IAAI;IAAEyB;EAAM,CAAC,GAAGN,IAAI;EAC5B,MAAMqC,YAAY,GAAG/B,KAAK,CAACgC,qBAAqB,CAAC,YAAY,CAAC;EAC9D,MAAMC,aAAa,GAAG1D,IAAI,CAAC2D,EAAE,IAAIxC,IAAI,CAACuC,aAAa,CAAC,CAAC;EACrD,MAAME,QAAQ,GAAGzC,IAAI,CAAC0C,cAAc,CAAC,CAAC;EACtC,MAAM;IAAEC;EAAW,CAAC,GAAG9D,IAAI;EAE3BA,IAAI,CAACwB,IAAI,GAAG,kBAAkB;EAC9B,IAAI,CAACxB,IAAI,CAAC2D,EAAE,EAAE3D,IAAI,CAAC2D,EAAE,GAAGnE,WAAC,CAACuE,SAAS,CAACT,GAAG,CAAC;EAExC,IAAIU,OAAqB;EACzB,IAAIF,UAAU,EAAE;IACdE,OAAO,GAAGvC,KAAK,CAACwC,gCAAgC,CAACjE,IAAI,CAAC8D,UAAU,EAAE,OAAO,CAAC;IAC1E9D,IAAI,CAAC8D,UAAU,GAAGE,OAAO;EAC3B;EAEA,MAAME,eAAe,GAAGnE,cAAc,CAACC,IAAI,CAAC;EAC5C,MAAMmE,WAAW,GAAG3E,WAAC,CAACY,eAAe,CACnCmD,QAAQ,CACLnB,MAAM,CACLgC,OAAO,IAEL,CAACA,OAAO,CAACpE,IAAI,CAACqE,QAAQ,IAAID,OAAO,CAACpE,IAAI,CAACwB,IAAI,KAAK,kBACpD,CAAC,CACAnB,GAAG,CAACc,IAAI,IACPJ,wBAAwB,CACtBC,IAAI,EACJhB,IAAI,CAAC2D,EAAE,EACPK,OAAO,EAEP7C,IACF,CACF,CACJ,CAAC;EAED,MAAMmD,WAAW,GAAGzB,cAAQ,CAACtC,UAAU,CAACwC,GAAG;AAC7C,MAAMI,iBAAiB,CAACnC,IAAI,CAAC;AAC7B,QAAQkD,eAAe,IAAI1E,WAAC,CAAC+E,WAAW,CAAC,CAAC;AAC1C,kBAAkBf,YAAY,KAAKM,UAAU,GAAGtE,WAAC,CAACuE,SAAS,CAACC,OAAO,CAAC,GAAG,IAAI;AAC3E,UAAUhE,IAAI;AACd,sBAAsBR,WAAC,CAACuE,SAAS,CAAC/D,IAAI,CAAC2D,EAAE,CAAC,QAAQQ,WAAW;AAC7D;AACA,QAAQL,UAAU;AAClB;AACA,GAAwE;EAEtE,IAAI,CAACF,QAAQ,EAAE;IACbU,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC5E,IAAI,CAAC6E,UAAU,CAAC/B,IAAI,CAC3ClD,WAAC,CAACkF,SAAS,CAAClF,WAAC,CAACmF,gBAAgB,CAAC,YAAY,CAAC,CAC9C,CAAC;EACH;EAEA,IAAIC,WAAmB,GAAGN,WAAW;EACrC,IAAIO,aAAa,GAAG,yBAAyB;EAC7C,IAAInB,aAAa,EAAE;IACjBkB,WAAW,GAAG/B,cAAQ,CAACiC,SAAS,CAAC/B,GAAG,OAAOO,GAAG,MAAMgB,WAAW,EAAE;IACjEO,aAAa,GAAG,sBAAsB,GAAGA,aAAa;EACxD;EAEA,OAAO;IACLE,aAAa,EAAE,CACblC,cAAQ,CAACiC,SAAS,CAAC/B,GAAG;AAC5B,UAAUvD,WAAC,CAACuE,SAAS,CAACP,YAAY,CAAC;AACnC,OAAO,CACF;IACDwB,SAASA,CAAC7D,IAAuB,EAAE;MACjCA,IAAI,CAAC8D,WAAW,CAACL,WAAW,CAAC;MAC7B,OAAOzD,IAAI,CAAC+D,GAAG,CAACL,aAAa,CAAC;IAChC;EACF,CAAC;AACH", "ignoreList": []}