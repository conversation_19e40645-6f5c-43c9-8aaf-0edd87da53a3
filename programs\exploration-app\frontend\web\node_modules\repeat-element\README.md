# repeat-element [![NPM version](https://img.shields.io/npm/v/repeat-element.svg?style=flat)](https://www.npmjs.com/package/repeat-element) [![NPM monthly downloads](https://img.shields.io/npm/dm/repeat-element.svg?style=flat)](https://npmjs.org/package/repeat-element) [![NPM total downloads](https://img.shields.io/npm/dt/repeat-element.svg?style=flat)](https://npmjs.org/package/repeat-element) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/repeat-element.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/repeat-element)

> Create an array by repeating the given value n times.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save repeat-element
```

## Usage

```js
const repeat = require('repeat-element');

repeat('a', 5);
//=> ['a', 'a', 'a', 'a', 'a']

repeat('a', 1);
//=> ['a']

repeat('a', 0);
//=> []

repeat(null, 5)
//» [ null, null, null, null, null ]

repeat({some: 'object'}, 5)
//» [ { some: 'object' },
//    { some: 'object' },
//    { some: 'object' },
//    { some: 'object' },
//    { some: 'object' } ]

repeat(5, 5)
//» [ 5, 5, 5, 5, 5 ]
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 17 | [jonschlinkert](https://github.com/jonschlinkert) |
| 3 | [LinusU](https://github.com/LinusU) |
| 1 | [architectcodes](https://github.com/architectcodes) |

### Author

**Jon Schlinkert**

* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)
* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on August 19, 2018._