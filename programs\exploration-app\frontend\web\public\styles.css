/* RDR2 Map Styling */

/* Root Variables */
:root {
    --parchment: #D8C7A0;
    --parchment-dark: #C4B490;
    --leather: #704214;
    --ink: #1A1A1A;
    --road: #3A2E24;
    --water: #A1A78B;
    --water-dark: #6C725F;
    --icon: #4E5A3E;
    --building: #C4B490;
}

/* Base Styles - Crisp Typography */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'IM Fell English', 'Cinzel', 'Times New Roman', serif;
    background-color: var(--parchment);
    color: var(--ink);
    font-size: 18px; /* Larger for better readability */
    line-height: 1.4;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Clean Parchment Overlay - No Blur */
#parchment-overlay {
    background:
        /* Very subtle paper texture only */
        linear-gradient(45deg, rgba(216, 199, 160, 0.02) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(196, 180, 144, 0.02) 25%, transparent 25%);
    background-size: 40px 40px, 40px 40px;
    opacity: 0.3;
    pointer-events: none;
    z-index: 500;
    /* No animation for stability */
}

/* Map Styling - Custom Hand-drawn Bethnal Green Map */
#map {
    width: 100%;
    height: 100vh;
    position: relative;
    z-index: 10;
    background-color: var(--parchment);
    /* NO filters - crisp and clear */
}

/* Remove all tile styling since we're not using external tiles */
.leaflet-tile {
    display: none; /* Hide external map tiles completely */
}

/* Comprehensive map text labels */
.map-text-label, .map-label, .zoom-dependent {
    pointer-events: none;
    z-index: 1000;
}

.map-text-label span, .map-label span, .zoom-dependent span {
    display: block;
    text-align: center;
    white-space: nowrap;
    font-family: 'IM Fell English', 'Cinzel', 'Times New Roman', serif !important;
}

/* Specific label styling */
.road-label span {
    background: rgba(216, 199, 160, 0.8);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid rgba(58, 46, 36, 0.3);
}

.park-label span {
    background: rgba(143, 188, 143, 0.8);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid rgba(78, 90, 62, 0.5);
}

.water-label span {
    background: rgba(161, 167, 139, 0.8);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid rgba(108, 114, 95, 0.5);
}

.station-label span, .pub-label span, .museum-label span,
.theatre-label span, .library-label span, .church-label span,
.market-label span, .school-label span, .hospital-label span,
.estate-label span, .building-label span {
    background: rgba(216, 199, 160, 0.9);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid rgba(58, 46, 36, 0.6);
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.decoration-label span {
    background: rgba(216, 199, 160, 0.7);
    padding: 4px 12px;
    border-radius: 6px;
    border: 2px solid rgba(58, 46, 36, 0.4);
    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
}

.historical-label span {
    background: rgba(139, 69, 19, 0.8);
    color: var(--parchment);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid rgba(58, 46, 36, 0.6);
    font-style: italic;
}

.terrain-label span {
    background: rgba(154, 205, 50, 0.8);
    padding: 3px 8px;
    border-radius: 4px;
    border: 1px solid rgba(78, 90, 62, 0.6);
    font-weight: 600;
}

.commercial-label span {
    background: rgba(255, 215, 0, 0.8);
    color: var(--ink);
    padding: 3px 8px;
    border-radius: 4px;
    border: 2px solid rgba(58, 46, 36, 0.6);
    font-weight: bold;
}

.weather-label span {
    background: rgba(216, 199, 160, 0.6);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid rgba(58, 46, 36, 0.3);
    font-size: 10px;
}

/* Remove modern map elements */
.leaflet-control-zoom {
    display: none !important;
}

.leaflet-control-attribution {
    background: rgba(112, 66, 20, 0.8) !important;
    color: var(--parchment) !important;
    font-family: 'IM Fell English', serif !important;
    font-size: 10px !important;
    border: 1px solid var(--road) !important;
    border-radius: 0 !important;
}

/* Map Border - Hand-drawn Style */
#map-border {
    border: none;
    pointer-events: none;
    z-index: 550;
    /* Create hand-drawn border effect */
    background:
        /* Corner decorations */
        radial-gradient(circle at 0% 0%, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 20px, var(--road) 20px, var(--road) 22px, transparent 22px),
        radial-gradient(circle at 100% 0%, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 20px, var(--road) 20px, var(--road) 22px, transparent 22px),
        radial-gradient(circle at 0% 100%, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 20px, var(--road) 20px, var(--road) 22px, transparent 22px),
        radial-gradient(circle at 100% 100%, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 20px, var(--road) 20px, var(--road) 22px, transparent 22px),
        /* Irregular border lines */
        linear-gradient(0deg, transparent 0%, transparent calc(100% - 8px), var(--road) calc(100% - 8px), var(--road) calc(100% - 6px), transparent calc(100% - 6px), transparent calc(100% - 4px), var(--road) calc(100% - 4px), var(--road) 100%),
        linear-gradient(180deg, transparent 0%, transparent calc(100% - 8px), var(--road) calc(100% - 8px), var(--road) calc(100% - 6px), transparent calc(100% - 6px), transparent calc(100% - 4px), var(--road) calc(100% - 4px), var(--road) 100%),
        linear-gradient(90deg, transparent 0%, transparent calc(100% - 8px), var(--road) calc(100% - 8px), var(--road) calc(100% - 6px), transparent calc(100% - 6px), transparent calc(100% - 4px), var(--road) calc(100% - 4px), var(--road) 100%),
        linear-gradient(270deg, transparent 0%, transparent calc(100% - 8px), var(--road) calc(100% - 8px), var(--road) calc(100% - 6px), transparent calc(100% - 6px), transparent calc(100% - 4px), var(--road) calc(100% - 4px), var(--road) 100%);
    opacity: 0.8;
    box-shadow:
        inset 0 0 50px rgba(58, 46, 36, 0.4),
        inset 0 0 100px rgba(112, 66, 20, 0.2);
}

/* Sidebar Styling - RDR2 Journal Style */
#sidebar {
    background:
        /* Leather texture with stains and wear */
        radial-gradient(circle at 20% 20%, rgba(58, 46, 36, 0.3) 0%, transparent 30%),
        radial-gradient(circle at 80% 60%, rgba(139, 69, 19, 0.2) 0%, transparent 25%),
        radial-gradient(circle at 40% 80%, rgba(58, 46, 36, 0.15) 0%, transparent 20%),
        linear-gradient(135deg, rgba(112, 66, 20, 0.95) 0%, rgba(139, 69, 19, 0.9) 100%);
    backdrop-filter: blur(8px);
    border-right: none;
    /* Hand-drawn border effect */
    box-shadow:
        inset -4px 0 0 0 var(--road),
        inset -6px 0 0 0 rgba(58, 46, 36, 0.5),
        inset -8px 0 0 0 rgba(58, 46, 36, 0.3),
        4px 0 15px rgba(0, 0, 0, 0.4);
    position: relative;
}

/* Add decorative corner elements */
#sidebar::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background:
        linear-gradient(45deg, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 8px, var(--road) 8px, var(--road) 10px, transparent 10px),
        linear-gradient(-45deg, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 8px, var(--road) 8px, var(--road) 10px, transparent 10px);
    opacity: 0.6;
}

#sidebar::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background:
        linear-gradient(45deg, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 8px, var(--road) 8px, var(--road) 10px, transparent 10px),
        linear-gradient(-45deg, var(--road) 0%, var(--road) 2px, transparent 2px, transparent 8px, var(--road) 8px, var(--road) 10px, transparent 10px);
    opacity: 0.6;
}

#sidebar h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    letter-spacing: 0.1em;
    position: relative;
    font-size: 24px;
    font-weight: bold;
}

/* Clean decorative underline */
#sidebar h1::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 2px;
    background: var(--parchment);
    opacity: 0.8;
}

#sidebar h3 {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    font-size: 18px;
    font-weight: 600;
}

/* Input Styling */
input[type="text"] {
    background-color: var(--parchment);
    color: var(--ink);
    border: 2px solid var(--road);
    font-family: 'IM Fell English', 'Cinzel', serif;
    font-size: 14px;
    transition: all 0.2s ease;
}

input[type="text"]:focus {
    border-color: var(--parchment-dark);
    box-shadow: 0 0 5px rgba(196, 180, 144, 0.5);
}

/* Button Styling */
button {
    background-color: var(--parchment);
    color: var(--ink);
    border: 2px solid var(--road);
    font-family: 'IM Fell English', 'Cinzel', serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.2s ease;
}

button:hover {
    background-color: var(--parchment-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Checkbox Styling */
input[type="checkbox"] {
    accent-color: var(--parchment);
    width: 16px;
    height: 16px;
}

/* Search Results - RDR2 Journal Style */
#results-list .result-item {
    background:
        /* Parchment with ink stains */
        radial-gradient(circle at 80% 20%, rgba(58, 46, 36, 0.08) 0%, transparent 30%),
        var(--parchment);
    color: var(--ink);
    padding: 12px 16px;
    margin: 6px 0;
    border: none;
    border-left: 3px solid var(--road);
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(112, 66, 20, 0.2);
    position: relative;
    /* Slight rotation for hand-written feel */
    transform: rotate(0.2deg);
    box-shadow:
        inset 0 0 0 1px rgba(58, 46, 36, 0.2),
        2px 2px 4px rgba(58, 46, 36, 0.3);
}

/* Add decorative bullet point */
#results-list .result-item::before {
    content: '◆';
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--road);
    font-size: 8px;
    opacity: 0.7;
}

#results-list .result-item:hover {
    background:
        radial-gradient(circle at 80% 20%, rgba(58, 46, 36, 0.12) 0%, transparent 30%),
        var(--parchment-dark);
    transform: translateX(8px) rotate(0deg);
    box-shadow:
        inset 0 0 0 1px rgba(58, 46, 36, 0.3),
        4px 4px 8px rgba(58, 46, 36, 0.4);
    border-left-width: 4px;
}

#results-list .result-item:nth-child(even) {
    transform: rotate(-0.1deg);
}

#results-list .result-item:nth-child(odd) {
    transform: rotate(0.3deg);
}

/* Compass Rose - Enhanced RDR2 Style */
#compass-rose {
    filter:
        drop-shadow(4px 4px 8px rgba(58, 46, 36, 0.8))
        drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.6));
    /* Subtle animation for living map feel */
    animation: compassFloat 6s ease-in-out infinite alternate;
}

@keyframes compassFloat {
    0% { transform: rotate(0deg) scale(1); }
    100% { transform: rotate(0.5deg) scale(1.02); }
}

/* Enhanced vintage corner effects with animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 0% 0%, rgba(58, 46, 36, 0.04) 0%, transparent 18%),
        radial-gradient(circle at 100% 100%, rgba(58, 46, 36, 0.04) 0%, transparent 18%),
        radial-gradient(circle at 100% 0%, rgba(112, 66, 20, 0.02) 0%, transparent 12%),
        radial-gradient(circle at 0% 100%, rgba(112, 66, 20, 0.02) 0%, transparent 12%);
    pointer-events: none;
    z-index: 1000;
    opacity: 0.6;
    animation: vintageFlicker 8s ease-in-out infinite alternate;
}

@keyframes vintageFlicker {
    0% { opacity: 0.5; }
    50% { opacity: 0.7; }
    100% { opacity: 0.6; }
}

/* Custom control styling */
.measure-control {
    background: rgba(112, 66, 20, 0.9);
    border: 2px solid var(--road);
    border-radius: 4px;
    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
}

.measure-control button {
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.measure-control button:hover {
    background: var(--parchment-dark) !important;
    color: var(--ink) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Context menu styling */
.context-menu {
    font-family: 'IM Fell English', serif;
    background: var(--parchment);
    border: 2px solid var(--road);
    border-radius: 4px;
    padding: 12px;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4);
}

.context-menu h4 {
    margin: 0 0 8px 0;
    color: var(--road);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.context-menu p {
    margin: 4px 0;
    font-size: 12px;
    color: var(--ink);
}

/* Enhanced exploration progress */
#exploration-progress {
    background: linear-gradient(135deg, var(--parchment) 0%, var(--parchment-dark) 100%);
    box-shadow: inset 2px 2px 4px rgba(58, 46, 36, 0.3);
    font-weight: bold;
    text-align: center;
    position: relative;
    overflow: hidden;
}

#exploration-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: progressShine 3s ease-in-out infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Loading Screen */
#loading {
    background-color: rgba(216, 199, 160, 0.95);
    backdrop-filter: blur(10px);
}

/* Clean Leaflet Popup Styling */
.leaflet-popup-content-wrapper {
    background: var(--parchment) !important;
    color: var(--ink) !important;
    border: 3px solid var(--road) !important;
    border-radius: 0 !important;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3) !important;
    /* No rotation for clarity */
}

.leaflet-popup-content {
    font-family: 'IM Fell English', 'Cinzel', 'Times New Roman', serif !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin: 16px 20px !important;
    font-weight: 500 !important;
    color: var(--ink) !important;
}

.leaflet-popup-tip {
    background: var(--parchment) !important;
    border: 2px solid var(--road) !important;
    border-top: none !important;
    border-right: none !important;
}

.leaflet-popup-close-button {
    color: var(--road) !important;
    font-size: 18px !important;
    font-weight: bold !important;
    padding: 4px 8px !important;
    background: var(--parchment) !important;
    border: 2px solid var(--road) !important;
    border-radius: 0 !important;
    font-family: 'IM Fell English', serif !important;
    transition: all 0.2s ease !important;
}

.leaflet-popup-close-button:hover {
    background: var(--parchment-dark) !important;
    color: var(--ink) !important;
}

/* Enhanced Custom Marker Styling */
.rdr2-marker {
    filter:
        drop-shadow(3px 3px 6px rgba(58, 46, 36, 0.6))
        drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.4))
        sepia(20%)
        contrast(1.2);
    transform: rotate(var(--marker-rotation, 0deg));
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: markerFloat 3s ease-in-out infinite alternate;
}

.rdr2-marker:hover {
    filter:
        drop-shadow(5px 5px 10px rgba(58, 46, 36, 0.9))
        drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.7))
        sepia(40%)
        contrast(1.5)
        brightness(1.2)
        hue-rotate(5deg);
    transform: rotate(var(--marker-rotation, 0deg)) scale(1.2);
    animation: markerPulse 0.6s ease-in-out infinite alternate;
}

@keyframes markerFloat {
    0% { transform: rotate(var(--marker-rotation, 0deg)) translateY(0px); }
    100% { transform: rotate(var(--marker-rotation, 0deg)) translateY(-2px); }
}

@keyframes markerPulse {
    0% { transform: rotate(var(--marker-rotation, 0deg)) scale(1.2); }
    100% { transform: rotate(var(--marker-rotation, 0deg)) scale(1.25); }
}

/* Enhanced random rotations with more variation */
.leaflet-marker-icon:nth-child(1) { --marker-rotation: 3deg; }
.leaflet-marker-icon:nth-child(2) { --marker-rotation: -2deg; }
.leaflet-marker-icon:nth-child(3) { --marker-rotation: 1.8deg; }
.leaflet-marker-icon:nth-child(4) { --marker-rotation: -2.5deg; }
.leaflet-marker-icon:nth-child(5) { --marker-rotation: 0.8deg; }
.leaflet-marker-icon:nth-child(6) { --marker-rotation: -1.8deg; }
.leaflet-marker-icon:nth-child(7) { --marker-rotation: 2.8deg; }
.leaflet-marker-icon:nth-child(8) { --marker-rotation: -0.8deg; }
.leaflet-marker-icon:nth-child(9) { --marker-rotation: 1.2deg; }
.leaflet-marker-icon:nth-child(10) { --marker-rotation: -3deg; }

/* Paper Reveal Animation - Enhanced */
@keyframes paperReveal {
    from {
        opacity: 0;
        transform: scale(0.95) rotate(0.2deg);
        filter: blur(1px);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: blur(0px);
    }
}

.leaflet-tile {
    animation: paperReveal 0.8s ease-out;
}

/* No territory overlay for maximum clarity */

/* Territory Boundary Styling */
.territory-boundary {
    filter: drop-shadow(2px 2px 4px rgba(58, 46, 36, 0.4));
}

.territory-boundary:hover {
    opacity: 0.8 !important;
    filter: drop-shadow(3px 3px 6px rgba(58, 46, 36, 0.6));
}

/* Responsive Design */
@media (max-width: 768px) {
    #sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        bottom: 0;
        top: auto;
        left: 0;
        right: 0;
        border-right: none;
        border-top: 2px solid var(--road);
        overflow-y: auto;
    }
    
    #map {
        height: 60vh;
    }
    
    #compass-rose {
        bottom: 45vh;
        right: 1rem;
        width: 20px;
        height: 20px;
    }
    
    body {
        font-size: 14px;
    }
    
    #sidebar h1 {
        font-size: 1.5rem;
    }
}

/* Scrollbar Styling */
#sidebar::-webkit-scrollbar {
    width: 8px;
}

#sidebar::-webkit-scrollbar-track {
    background: rgba(58, 46, 36, 0.3);
    border-radius: 4px;
}

#sidebar::-webkit-scrollbar-thumb {
    background: var(--parchment);
    border-radius: 4px;
    border: 1px solid var(--road);
}

#sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--parchment-dark);
}
