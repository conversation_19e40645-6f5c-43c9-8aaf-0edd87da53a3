{"version": 3, "names": ["_iterableToArray", "iter", "Symbol", "iterator", "Array", "from"], "sources": ["../../src/helpers/iterableToArray.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _iterableToArray<T>(iter: Iterable<T>) {\n  if (\n    (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null) ||\n    (iter as any)[\"@@iterator\"] != null\n  ) {\n    return Array.from(iter);\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,gBAAgBA,CAAIC,IAAiB,EAAE;EAC7D,IACG,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAC9DF,IAAI,CAAS,YAAY,CAAC,IAAI,IAAI,EACnC;IACA,OAAOG,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC;EACzB;AACF", "ignoreList": []}